using UnityEngine;
using UnityEditor;

/// <summary>
/// Custom Editor cho MeshOptimizer với giao diện thân thiện
/// </summary>
[CustomEditor(typeof(MeshOptimizer))]
public class MeshOptimizerEditor : Editor
{
    private MeshOptimizer meshOptimizer;
    private bool showAdvancedSettings = false;
    private bool showStats = false;

    private void OnEnable()
    {
        meshOptimizer = (MeshOptimizer)target;
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        DrawHeader();
        DrawQuickActions();
        DrawBasicSettings();
        DrawAdvancedSettings();
        DrawStatistics();
        DrawUtilities();

        serializedObject.ApplyModifiedProperties();
    }

    private void DrawHeader()
    {
        EditorGUILayout.Space();
        GUIStyle headerStyle = new GUIStyle(EditorStyles.boldLabel)
        {
            fontSize = 16,
            alignment = TextAnchor.MiddleCenter
        };
        EditorGUILayout.LabelField("🔧 MESH OPTIMIZER", headerStyle);
        EditorGUILayout.LabelField("Tối ưu hóa mesh cho môi trường", EditorStyles.centeredGreyMiniLabel);
        EditorGUILayout.Space();
    }

    private void DrawQuickActions()
    {
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("⚡ Thao Tác Nhanh", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        
        GUI.backgroundColor = Color.green;
        if (GUILayout.Button("🚀 Tối Ưu Ngay", GUILayout.Height(30)))
        {
            if (meshOptimizer.CanOptimize())
            {
                meshOptimizer.OptimizeMeshes();
                EditorUtility.SetDirty(meshOptimizer);
            }
            else
            {
                EditorUtility.DisplayDialog("Không thể tối ưu", 
                    "Cần ít nhất 2 mesh con để có thể tối ưu hóa.", "OK");
            }
        }

        GUI.backgroundColor = Color.yellow;
        if (GUILayout.Button("📊 Thống Kê", GUILayout.Height(30)))
        {
            meshOptimizer.PrintOptimizationStats();
        }

        GUI.backgroundColor = Color.red;
        if (GUILayout.Button("🔄 Khôi Phục", GUILayout.Height(30)))
        {
            if (EditorUtility.DisplayDialog("Khôi phục mesh", 
                "Bạn có chắc muốn khôi phục về trạng thái ban đầu?", "Có", "Không"))
            {
                meshOptimizer.RestoreToOriginal();
                EditorUtility.SetDirty(meshOptimizer);
            }
        }

        GUI.backgroundColor = Color.white;
        EditorGUILayout.EndHorizontal();

        // Hiển thị trạng thái
        EditorGUILayout.Space(5);
        string statusText = meshOptimizer.CombinedObjectsCount > 0 
            ? $"✅ Đã tối ưu: {meshOptimizer.CombinedObjectsCount} combined objects"
            : "⏳ Chưa tối ưu";
        EditorGUILayout.LabelField("Trạng thái:", statusText, EditorStyles.miniLabel);

        EditorGUILayout.EndVertical();
    }

    private void DrawBasicSettings()
    {
        EditorGUILayout.Space();
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("⚙️ Cài Đặt Cơ Bản", EditorStyles.boldLabel);

        SerializedProperty settings = serializedObject.FindProperty("settings");
        
        EditorGUILayout.PropertyField(settings.FindPropertyRelative("combineOnStart"), 
            new GUIContent("Tự động tối ưu khi Start", "Tự động chạy tối ưu hóa khi game bắt đầu"));
        
        EditorGUILayout.PropertyField(settings.FindPropertyRelative("combineByMaterial"), 
            new GUIContent("Gộp theo Material", "Gộp mesh có cùng material để tối ưu batching"));
        
        EditorGUILayout.PropertyField(settings.FindPropertyRelative("generateLODs"), 
            new GUIContent("Tạo LOD", "Tự động tạo các mức LOD để tối ưu hiệu suất"));
        
        EditorGUILayout.PropertyField(settings.FindPropertyRelative("preserveOriginalMeshes"), 
            new GUIContent("Giữ lại Mesh gốc", "Không ẩn mesh gốc sau khi tối ưu"));

        EditorGUILayout.EndVertical();
    }

    private void DrawAdvancedSettings()
    {
        EditorGUILayout.Space();
        showAdvancedSettings = EditorGUILayout.Foldout(showAdvancedSettings, "🔧 Cài Đặt Nâng Cao", true);
        
        if (showAdvancedSettings)
        {
            EditorGUILayout.BeginVertical("box");
            SerializedProperty settings = serializedObject.FindProperty("settings");

            // Mesh Settings
            EditorGUILayout.LabelField("Mesh Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("maxVerticesPerMesh"), 
                new GUIContent("Max Vertices/Mesh", "Số vertices tối đa cho mỗi combined mesh"));

            EditorGUILayout.Space();

            // Vertex Optimization Settings
            EditorGUILayout.LabelField("Vertex Optimization", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("enableVertexWelding"),
                new GUIContent("Enable Vertex Welding", "Gộp các đỉnh gần nhau"));
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("vertexWeldThreshold"),
                new GUIContent("Weld Threshold", "Khoảng cách tối đa để gộp vertices"));
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("removeUnusedVertices"),
                new GUIContent("Remove Unused Vertices", "Xóa vertices không được sử dụng"));
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("optimizeVertexOrder"),
                new GUIContent("Optimize Vertex Order", "Tối ưu thứ tự vertices cho cache"));

            EditorGUILayout.Space();

            // LOD Settings
            EditorGUILayout.LabelField("LOD Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("lod1Quality"), 
                new GUIContent("LOD 1 Quality", "Chất lượng LOD 1 (0.1 - 1.0)"));
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("lod2Quality"), 
                new GUIContent("LOD 2 Quality", "Chất lượng LOD 2 (0.1 - 1.0)"));
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("lod3Quality"), 
                new GUIContent("LOD 3 Quality", "Chất lượng LOD 3 (0.1 - 1.0)"));

            EditorGUILayout.Space();

            // Culling Settings
            EditorGUILayout.LabelField("Culling Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("enableOcclusionCulling"), 
                new GUIContent("Enable Occlusion Culling", "Bật occlusion culling"));
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("cullDistance"), 
                new GUIContent("Cull Distance", "Khoảng cách culling"));
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("cullLayers"), 
                new GUIContent("Cull Layers", "Layers được áp dụng culling"));

            EditorGUILayout.Space();

            // Spatial Partitioning
            EditorGUILayout.LabelField("Spatial Partitioning", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("useSpatialPartitioning"), 
                new GUIContent("Use Spatial Partitioning", "Chia mesh theo vùng không gian"));
            EditorGUILayout.PropertyField(settings.FindPropertyRelative("chunkSize"), 
                new GUIContent("Chunk Size", "Kích thước mỗi chunk"));

            EditorGUILayout.EndVertical();
        }
    }

    private void DrawStatistics()
    {
        EditorGUILayout.Space();
        showStats = EditorGUILayout.Foldout(showStats, "📊 Thống Kê", true);
        
        if (showStats)
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField($"Combined Objects: {meshOptimizer.CombinedObjectsCount}");
            EditorGUILayout.LabelField($"Generated Meshes: {meshOptimizer.GeneratedMeshesCount}");
            
            if (meshOptimizer.CombinedObjectsCount > 0)
            {
                EditorGUILayout.Space();
                if (GUILayout.Button("📋 In Thống Kê Chi Tiết"))
                {
                    meshOptimizer.PrintOptimizationStats();
                }
            }
            
            EditorGUILayout.EndVertical();
        }
    }

    private void DrawUtilities()
    {
        EditorGUILayout.Space();
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("🛠️ Tiện Ích", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("🔍 Kiểm Tra Mesh"))
        {
            bool canOptimize = meshOptimizer.CanOptimize();
            string message = canOptimize
                ? "Có thể tối ưu hóa mesh này."
                : "Không thể tối ưu hóa. Cần ít nhất 2 mesh con.";
            EditorUtility.DisplayDialog("Kiểm Tra Mesh", message, "OK");
        }

        if (GUILayout.Button("🔬 Phân Tích Vertices"))
        {
            meshOptimizer.AnalyzeVertices();
        }

        if (GUILayout.Button("📖 Hướng Dẫn"))
        {
            ShowHelpDialog();
        }

        EditorGUILayout.EndHorizontal();
        EditorGUILayout.EndVertical();
    }

    private void ShowHelpDialog()
    {
        string helpText = @"🔧 HƯỚNG DẪN SỬ DỤNG MESH OPTIMIZER

1. 📋 CHUẨN BỊ:
   • Đặt script này vào GameObject cha
   • Đảm bảo có ít nhất 2 mesh con

2. ⚙️ CÀI ĐẶT:
   • Combine by Material: Gộp theo material để tối ưu batching
   • Generate LODs: Tạo LOD tự động
   • Spatial Partitioning: Chia theo vùng không gian
   • Vertex Welding: Gộp các đỉnh gần nhau
   • Remove Unused Vertices: Xóa vertices thừa

3. 🚀 SỬ DỤNG:
   • Nhấn 'Tối Ưu Ngay' để bắt đầu
   • Xem thống kê để kiểm tra hiệu quả
   • Dùng 'Khôi Phục' để hoàn tác

4. 💡 MẸO:
   • Dùng cho môi trường tĩnh
   • Kiểm tra LOD distance phù hợp
   • Backup scene trước khi tối ưu";

        EditorUtility.DisplayDialog("Hướng Dẫn Mesh Optimizer", helpText, "Đã hiểu");
    }
}
