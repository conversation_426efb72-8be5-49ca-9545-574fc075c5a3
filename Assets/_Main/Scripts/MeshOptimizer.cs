using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// <PERSON><PERSON> thống tối ưu hóa mesh nâng cao cho môi trường với nhiều mesh tĩnh.
/// Hỗ trợ gộp mesh theo material, LOD, occlusion culling và spatial partitioning.
/// </summary>
public class MeshOptimizer : MonoBehaviour
{
    [System.Serializable]
    public class OptimizationSettings
    {
        [Header("Cài Đặt Cơ Bản")]
        public bool combineOnStart = true;
        public bool preserveOriginalMeshes = false;
        public bool generateLODs = true;

        [Header("Gộp Mesh Theo Material")]
        public bool combineByMaterial = true;
        public int maxVerticesPerMesh = 65000; // Giới hạn Unity

        [Header("Vertex Optimization")]
        public bool enableVertexWelding = true;
        [Range(0.001f, 0.1f)]
        public float vertexWeldThreshold = 0.01f;
        public bool optimizeVertexOrder = true;
        public bool removeUnusedVertices = true;

        [Header("LOD Settings")]
        [Range(0.1f, 1f)]
        public float lod1Quality = 0.7f;
        [Range(0.1f, 1f)]
        public float lod2Quality = 0.4f;
        [Range(0.1f, 1f)]
        public float lod3Quality = 0.2f;

        [Header("Culling Settings")]
        public bool enableOcclusionCulling = true;
        public float cullDistance = 100f;
        public LayerMask cullLayers = -1;

        [Header("Spatial Partitioning")]
        public bool useSpatialPartitioning = true;
        public Vector3 chunkSize = new Vector3(50, 50, 50);
    }

    [SerializeField] private OptimizationSettings settings = new OptimizationSettings();

    // Cache và quản lý
    private Dictionary<Material, List<CombineInstance>> materialGroups = new Dictionary<Material, List<CombineInstance>>();
    private List<GameObject> combinedObjects = new List<GameObject>();
    private List<Mesh> generatedMeshes = new List<Mesh>();
    private Dictionary<Vector3Int, List<MeshRenderer>> spatialChunks = new Dictionary<Vector3Int, List<MeshRenderer>>();

    // Properties
    public OptimizationSettings Settings => settings;
    public int CombinedObjectsCount => combinedObjects.Count;
    public int GeneratedMeshesCount => generatedMeshes.Count;

    private void Start()
    {
        if (settings.combineOnStart)
        {
            OptimizeMeshes();
        }
    }

    private void OnDestroy()
    {
        CleanupGeneratedMeshes();
    }

    [ContextMenu("Tối Ưu Mesh (Optimize Meshes)")]
    public void OptimizeMeshes()
    {
        CleanupPreviousOptimization();

        if (settings.useSpatialPartitioning)
        {
            OptimizeWithSpatialPartitioning();
        }
        else
        {
            OptimizeAllMeshes();
        }

        Debug.Log($"✅ Tối ưu hóa hoàn tất! Tạo {combinedObjects.Count} combined objects và {generatedMeshes.Count} meshes.");
    }

    /// <summary>
    /// Tối ưu hóa tất cả mesh trong object
    /// </summary>
    private void OptimizeAllMeshes()
    {
        MeshRenderer[] meshRenderers = GetComponentsInChildren<MeshRenderer>();

        if (settings.combineByMaterial)
        {
            GroupMeshesByMaterial(meshRenderers);
            CombineMeshesByMaterial();
        }
        else
        {
            CombineAllMeshes(meshRenderers);
        }
    }

    /// <summary>
    /// Tối ưu hóa với spatial partitioning
    /// </summary>
    private void OptimizeWithSpatialPartitioning()
    {
        MeshRenderer[] meshRenderers = GetComponentsInChildren<MeshRenderer>();

        // Chia mesh theo vùng không gian
        PartitionMeshesSpatially(meshRenderers);

        // Tối ưu từng chunk
        foreach (var chunk in spatialChunks)
        {
            if (chunk.Value.Count > 1) // Chỉ gộp nếu có nhiều hơn 1 mesh
            {
                OptimizeChunk(chunk.Key, chunk.Value);
            }
        }
    }

    /// <summary>
    /// Chia mesh theo vùng không gian
    /// </summary>
    private void PartitionMeshesSpatially(MeshRenderer[] meshRenderers)
    {
        spatialChunks.Clear();

        foreach (MeshRenderer renderer in meshRenderers)
        {
            if (renderer.transform == transform) continue;

            Vector3 worldPos = renderer.bounds.center;
            Vector3Int chunkCoord = new Vector3Int(
                Mathf.FloorToInt(worldPos.x / settings.chunkSize.x),
                Mathf.FloorToInt(worldPos.y / settings.chunkSize.y),
                Mathf.FloorToInt(worldPos.z / settings.chunkSize.z)
            );

            if (!spatialChunks.ContainsKey(chunkCoord))
            {
                spatialChunks[chunkCoord] = new List<MeshRenderer>();
            }

            spatialChunks[chunkCoord].Add(renderer);
        }

        Debug.Log($"Chia thành {spatialChunks.Count} chunks không gian.");
    }

    /// <summary>
    /// Tối ưu một chunk cụ thể
    /// </summary>
    private void OptimizeChunk(Vector3Int chunkCoord, List<MeshRenderer> renderers)
    {
        if (settings.combineByMaterial)
        {
            GroupMeshesByMaterial(renderers.ToArray());
            CombineMeshesByMaterial($"Chunk_{chunkCoord.x}_{chunkCoord.y}_{chunkCoord.z}");
        }
        else
        {
            CombineAllMeshes(renderers.ToArray(), $"Chunk_{chunkCoord.x}_{chunkCoord.y}_{chunkCoord.z}");
        }
    }

    /// <summary>
    /// Nhóm mesh theo material
    /// </summary>
    private void GroupMeshesByMaterial(MeshRenderer[] meshRenderers)
    {
        materialGroups.Clear();

        foreach (MeshRenderer renderer in meshRenderers)
        {
            if (renderer.transform == transform) continue;

            MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter == null || meshFilter.sharedMesh == null) continue;

            Material material = renderer.sharedMaterial;
            if (material == null) continue;

            if (!materialGroups.ContainsKey(material))
            {
                materialGroups[material] = new List<CombineInstance>();
            }

            CombineInstance combine = new CombineInstance
            {
                mesh = meshFilter.sharedMesh,
                transform = meshFilter.transform.localToWorldMatrix
            };

            materialGroups[material].Add(combine);
        }

        Debug.Log($"Nhóm mesh thành {materialGroups.Count} nhóm material.");
    }

    /// <summary>
    /// Gộp mesh theo material
    /// </summary>
    private void CombineMeshesByMaterial(string namePrefix = "CombinedMesh")
    {
        foreach (var materialGroup in materialGroups)
        {
            Material material = materialGroup.Key;
            List<CombineInstance> combines = materialGroup.Value;

            // Chia nhỏ nếu vượt quá giới hạn vertices
            List<List<CombineInstance>> batches = SplitIntoBatches(combines, settings.maxVerticesPerMesh);

            for (int batchIndex = 0; batchIndex < batches.Count; batchIndex++)
            {
                string objectName = batches.Count > 1
                    ? $"{namePrefix}_{material.name}_Batch{batchIndex}"
                    : $"{namePrefix}_{material.name}";

                CreateCombinedMesh(batches[batchIndex].ToArray(), material, objectName);
            }
        }
    }

    /// <summary>
    /// Gộp tất cả mesh thành một
    /// </summary>
    private void CombineAllMeshes(MeshRenderer[] meshRenderers, string objectName = "CombinedMesh")
    {
        List<CombineInstance> combines = new List<CombineInstance>();
        Material firstMaterial = null;

        foreach (MeshRenderer renderer in meshRenderers)
        {
            if (renderer.transform == transform) continue;

            MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter == null || meshFilter.sharedMesh == null) continue;

            CombineInstance combine = new CombineInstance
            {
                mesh = meshFilter.sharedMesh,
                transform = meshFilter.transform.localToWorldMatrix
            };

            combines.Add(combine);

            if (firstMaterial == null)
                firstMaterial = renderer.sharedMaterial;
        }

        if (combines.Count > 0)
        {
            CreateCombinedMesh(combines.ToArray(), firstMaterial, objectName);
        }
    }

    /// <summary>
    /// Chia combines thành các batch nhỏ hơn
    /// </summary>
    private List<List<CombineInstance>> SplitIntoBatches(List<CombineInstance> combines, int maxVertices)
    {
        List<List<CombineInstance>> batches = new List<List<CombineInstance>>();
        List<CombineInstance> currentBatch = new List<CombineInstance>();
        int currentVertexCount = 0;

        foreach (CombineInstance combine in combines)
        {
            int meshVertexCount = combine.mesh.vertexCount;

            if (currentVertexCount + meshVertexCount > maxVertices && currentBatch.Count > 0)
            {
                batches.Add(currentBatch);
                currentBatch = new List<CombineInstance>();
                currentVertexCount = 0;
            }

            currentBatch.Add(combine);
            currentVertexCount += meshVertexCount;
        }

        if (currentBatch.Count > 0)
        {
            batches.Add(currentBatch);
        }

        return batches;
    }

    /// <summary>
    /// Tạo combined mesh object
    /// </summary>
    private void CreateCombinedMesh(CombineInstance[] combines, Material material, string objectName)
    {
        // Tạo GameObject mới
        GameObject combinedObject = new GameObject(objectName);
        combinedObject.transform.SetParent(transform);
        combinedObject.transform.localPosition = Vector3.zero;
        combinedObject.transform.localRotation = Quaternion.identity;
        combinedObject.transform.localScale = Vector3.one;

        // Thêm components
        MeshFilter meshFilter = combinedObject.AddComponent<MeshFilter>();
        MeshRenderer meshRenderer = combinedObject.AddComponent<MeshRenderer>();

        // Tạo mesh
        Mesh combinedMesh = new Mesh();
        combinedMesh.name = objectName;
        combinedMesh.CombineMeshes(combines, true, true);

        // Tối ưu mesh với vertex welding
        if (settings.enableVertexWelding || settings.removeUnusedVertices || settings.optimizeVertexOrder)
        {
            combinedMesh = OptimizeMeshVertices(combinedMesh);
        }

        combinedMesh.Optimize();
        combinedMesh.RecalculateNormals();
        combinedMesh.RecalculateBounds();

        meshFilter.mesh = combinedMesh;
        meshRenderer.sharedMaterial = material;

        // Thêm vào cache
        combinedObjects.Add(combinedObject);
        generatedMeshes.Add(combinedMesh);

        // Tạo LOD nếu được bật
        if (settings.generateLODs)
        {
            CreateLODGroup(combinedObject, combinedMesh, material);
        }

        // Ẩn mesh gốc
        HideOriginalMeshes(combines);

        Debug.Log($"Tạo combined mesh: {objectName} với {combinedMesh.vertexCount} vertices.");
    }

    /// <summary>
    /// Tạo LOD Group cho combined mesh
    /// </summary>
    private void CreateLODGroup(GameObject combinedObject, Mesh originalMesh, Material material)
    {
        LODGroup lodGroup = combinedObject.AddComponent<LODGroup>();
        LOD[] lods = new LOD[4];

        // LOD 0 - Original mesh
        Renderer[] lod0Renderers = { combinedObject.GetComponent<MeshRenderer>() };
        lods[0] = new LOD(1.0f, lod0Renderers);

        // LOD 1
        GameObject lod1Object = CreateLODMesh(combinedObject, originalMesh, material, settings.lod1Quality, "_LOD1");
        if (lod1Object != null)
        {
            Renderer[] lod1Renderers = { lod1Object.GetComponent<MeshRenderer>() };
            lods[1] = new LOD(0.6f, lod1Renderers);
        }

        // LOD 2
        GameObject lod2Object = CreateLODMesh(combinedObject, originalMesh, material, settings.lod2Quality, "_LOD2");
        if (lod2Object != null)
        {
            Renderer[] lod2Renderers = { lod2Object.GetComponent<MeshRenderer>() };
            lods[2] = new LOD(0.3f, lod2Renderers);
        }

        // LOD 3
        GameObject lod3Object = CreateLODMesh(combinedObject, originalMesh, material, settings.lod3Quality, "_LOD3");
        if (lod3Object != null)
        {
            Renderer[] lod3Renderers = { lod3Object.GetComponent<MeshRenderer>() };
            lods[3] = new LOD(0.1f, lod3Renderers);
        }

        lodGroup.SetLODs(lods);
        lodGroup.RecalculateBounds();
    }

    /// <summary>
    /// Tạo LOD mesh với quality thấp hơn
    /// </summary>
    private GameObject CreateLODMesh(GameObject parent, Mesh originalMesh, Material material, float quality, string suffix)
    {
        try
        {
            // Tạo simplified mesh (đơn giản hóa bằng cách giảm vertices)
            Mesh lodMesh = SimplifyMesh(originalMesh, quality);
            if (lodMesh == null) return null;

            GameObject lodObject = new GameObject(parent.name + suffix);
            lodObject.transform.SetParent(parent.transform);
            lodObject.transform.localPosition = Vector3.zero;
            lodObject.transform.localRotation = Quaternion.identity;
            lodObject.transform.localScale = Vector3.one;

            MeshFilter meshFilter = lodObject.AddComponent<MeshFilter>();
            MeshRenderer meshRenderer = lodObject.AddComponent<MeshRenderer>();

            meshFilter.mesh = lodMesh;
            meshRenderer.sharedMaterial = material;

            generatedMeshes.Add(lodMesh);

            return lodObject;
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Không thể tạo LOD mesh: {e.Message}");
            return null;
        }
    }

    /// <summary>
    /// Đơn giản hóa mesh (basic implementation)
    /// </summary>
    private Mesh SimplifyMesh(Mesh originalMesh, float quality)
    {
        if (quality >= 1.0f) return originalMesh;

        Vector3[] originalVertices = originalMesh.vertices;
        int[] originalTriangles = originalMesh.triangles;
        Vector2[] originalUVs = originalMesh.uv;
        Vector3[] originalNormals = originalMesh.normals;

        int targetVertexCount = Mathf.RoundToInt(originalVertices.Length * quality);
        if (targetVertexCount < 3) return null;

        // Đơn giản hóa bằng cách lấy mẫu vertices
        int step = Mathf.Max(1, originalVertices.Length / targetVertexCount);

        List<Vector3> newVertices = new List<Vector3>();
        List<Vector2> newUVs = new List<Vector2>();
        List<Vector3> newNormals = new List<Vector3>();
        List<int> newTriangles = new List<int>();

        Dictionary<int, int> vertexMapping = new Dictionary<int, int>();

        // Lấy mẫu vertices
        for (int i = 0; i < originalVertices.Length; i += step)
        {
            vertexMapping[i] = newVertices.Count;
            newVertices.Add(originalVertices[i]);

            if (i < originalUVs.Length)
                newUVs.Add(originalUVs[i]);

            if (i < originalNormals.Length)
                newNormals.Add(originalNormals[i]);
        }

        // Tạo triangles mới
        for (int i = 0; i < originalTriangles.Length; i += 3)
        {
            int v1 = originalTriangles[i];
            int v2 = originalTriangles[i + 1];
            int v3 = originalTriangles[i + 2];

            if (vertexMapping.ContainsKey(v1) && vertexMapping.ContainsKey(v2) && vertexMapping.ContainsKey(v3))
            {
                newTriangles.Add(vertexMapping[v1]);
                newTriangles.Add(vertexMapping[v2]);
                newTriangles.Add(vertexMapping[v3]);
            }
        }

        if (newTriangles.Count == 0) return null;

        Mesh simplifiedMesh = new Mesh();
        simplifiedMesh.name = originalMesh.name + "_Simplified";
        simplifiedMesh.vertices = newVertices.ToArray();
        simplifiedMesh.triangles = newTriangles.ToArray();

        if (newUVs.Count > 0)
            simplifiedMesh.uv = newUVs.ToArray();

        if (newNormals.Count > 0)
            simplifiedMesh.normals = newNormals.ToArray();
        else
            simplifiedMesh.RecalculateNormals();

        simplifiedMesh.RecalculateBounds();

        return simplifiedMesh;
    }

    /// <summary>
    /// Tối ưu vertices của mesh (welding, removing unused, reordering)
    /// </summary>
    private Mesh OptimizeMeshVertices(Mesh originalMesh)
    {
        if (originalMesh == null) return null;

        Vector3[] vertices = originalMesh.vertices;
        Vector3[] normals = originalMesh.normals;
        Vector2[] uvs = originalMesh.uv;
        Color[] colors = originalMesh.colors;
        int[] triangles = originalMesh.triangles;

        int originalVertexCount = vertices.Length;

        // Bước 1: Vertex Welding (gộp các đỉnh gần nhau)
        if (settings.enableVertexWelding)
        {
            WeldVertices(ref vertices, ref normals, ref uvs, ref colors, ref triangles, settings.vertexWeldThreshold);
        }

        // Bước 2: Remove unused vertices
        if (settings.removeUnusedVertices)
        {
            RemoveUnusedVertices(ref vertices, ref normals, ref uvs, ref colors, ref triangles);
        }

        // Bước 3: Optimize vertex order for cache efficiency
        if (settings.optimizeVertexOrder)
        {
            OptimizeVertexOrder(ref vertices, ref normals, ref uvs, ref colors, ref triangles);
        }

        // Tạo mesh mới
        Mesh optimizedMesh = new Mesh();
        optimizedMesh.name = originalMesh.name + "_VertexOptimized";
        optimizedMesh.vertices = vertices;
        optimizedMesh.triangles = triangles;

        if (normals != null && normals.Length == vertices.Length)
            optimizedMesh.normals = normals;
        else
            optimizedMesh.RecalculateNormals();

        if (uvs != null && uvs.Length == vertices.Length)
            optimizedMesh.uv = uvs;

        if (colors != null && colors.Length == vertices.Length)
            optimizedMesh.colors = colors;

        int newVertexCount = vertices.Length;
        float reduction = (1f - (float)newVertexCount / originalVertexCount) * 100f;

        Debug.Log($"Vertex optimization: {originalVertexCount} → {newVertexCount} vertices ({reduction:F1}% reduction)");

        return optimizedMesh;
    }

    /// <summary>
    /// Gộp các đỉnh gần nhau (Vertex Welding)
    /// </summary>
    private void WeldVertices(ref Vector3[] vertices, ref Vector3[] normals, ref Vector2[] uvs,
                             ref Color[] colors, ref int[] triangles, float threshold)
    {
        Dictionary<int, int> vertexMapping = new Dictionary<int, int>();
        List<Vector3> newVertices = new List<Vector3>();
        List<Vector3> newNormals = new List<Vector3>();
        List<Vector2> newUVs = new List<Vector2>();
        List<Color> newColors = new List<Color>();

        float thresholdSqr = threshold * threshold;

        // Tìm và gộp vertices
        for (int i = 0; i < vertices.Length; i++)
        {
            bool found = false;

            // Tìm vertex gần nhất đã có
            for (int j = 0; j < newVertices.Count; j++)
            {
                float distanceSqr = (vertices[i] - newVertices[j]).sqrMagnitude;

                if (distanceSqr <= thresholdSqr)
                {
                    // Kiểm tra thêm normal và UV nếu có
                    bool normalMatch = normals == null || normals.Length <= i || normals.Length <= j ||
                                     Vector3.Dot(normals[i], newNormals[j]) > 0.9f;

                    bool uvMatch = uvs == null || uvs.Length <= i || uvs.Length <= j ||
                                 (uvs[i] - newUVs[j]).sqrMagnitude <= 0.01f;

                    if (normalMatch && uvMatch)
                    {
                        vertexMapping[i] = j;
                        found = true;
                        break;
                    }
                }
            }

            if (!found)
            {
                // Thêm vertex mới
                vertexMapping[i] = newVertices.Count;
                newVertices.Add(vertices[i]);

                if (normals != null && i < normals.Length)
                    newNormals.Add(normals[i]);

                if (uvs != null && i < uvs.Length)
                    newUVs.Add(uvs[i]);

                if (colors != null && i < colors.Length)
                    newColors.Add(colors[i]);
            }
        }

        // Cập nhật triangles
        for (int i = 0; i < triangles.Length; i++)
        {
            triangles[i] = vertexMapping[triangles[i]];
        }

        // Cập nhật arrays
        vertices = newVertices.ToArray();
        normals = newNormals.Count > 0 ? newNormals.ToArray() : null;
        uvs = newUVs.Count > 0 ? newUVs.ToArray() : null;
        colors = newColors.Count > 0 ? newColors.ToArray() : null;
    }

    /// <summary>
    /// Xóa các vertices không được sử dụng
    /// </summary>
    private void RemoveUnusedVertices(ref Vector3[] vertices, ref Vector3[] normals, ref Vector2[] uvs,
                                     ref Color[] colors, ref int[] triangles)
    {
        HashSet<int> usedVertices = new HashSet<int>();

        // Tìm vertices được sử dụng
        foreach (int vertexIndex in triangles)
        {
            usedVertices.Add(vertexIndex);
        }

        if (usedVertices.Count == vertices.Length) return; // Không có vertex nào thừa

        // Tạo mapping từ old index sang new index
        Dictionary<int, int> vertexMapping = new Dictionary<int, int>();
        List<Vector3> newVertices = new List<Vector3>();
        List<Vector3> newNormals = new List<Vector3>();
        List<Vector2> newUVs = new List<Vector2>();
        List<Color> newColors = new List<Color>();

        int newIndex = 0;
        for (int i = 0; i < vertices.Length; i++)
        {
            if (usedVertices.Contains(i))
            {
                vertexMapping[i] = newIndex++;
                newVertices.Add(vertices[i]);

                if (normals != null && i < normals.Length)
                    newNormals.Add(normals[i]);

                if (uvs != null && i < uvs.Length)
                    newUVs.Add(uvs[i]);

                if (colors != null && i < colors.Length)
                    newColors.Add(colors[i]);
            }
        }

        // Cập nhật triangles
        for (int i = 0; i < triangles.Length; i++)
        {
            triangles[i] = vertexMapping[triangles[i]];
        }

        // Cập nhật arrays
        vertices = newVertices.ToArray();
        normals = newNormals.Count > 0 ? newNormals.ToArray() : null;
        uvs = newUVs.Count > 0 ? newUVs.ToArray() : null;
        colors = newColors.Count > 0 ? newColors.ToArray() : null;
    }

    /// <summary>
    /// Tối ưu thứ tự vertices để tăng hiệu quả vertex cache
    /// </summary>
    private void OptimizeVertexOrder(ref Vector3[] vertices, ref Vector3[] normals, ref Vector2[] uvs,
                                    ref Color[] colors, ref int[] triangles)
    {
        // Sử dụng thuật toán đơn giản để sắp xếp lại vertices
        // Dựa trên thứ tự xuất hiện trong triangles

        Dictionary<int, int> vertexUsageOrder = new Dictionary<int, int>();

        // Đếm thứ tự sử dụng vertices
        for (int i = 0; i < triangles.Length; i++)
        {
            int vertexIndex = triangles[i];
            if (!vertexUsageOrder.ContainsKey(vertexIndex))
            {
                vertexUsageOrder[vertexIndex] = i;
            }
        }

        // Tạo danh sách vertices theo thứ tự sử dụng
        var sortedVertices = vertexUsageOrder.OrderBy(kvp => kvp.Value).Select(kvp => kvp.Key).ToList();

        // Tạo mapping từ old index sang new index
        Dictionary<int, int> vertexMapping = new Dictionary<int, int>();
        for (int i = 0; i < sortedVertices.Count; i++)
        {
            vertexMapping[sortedVertices[i]] = i;
        }

        // Tạo arrays mới theo thứ tự tối ưu
        Vector3[] newVertices = new Vector3[vertices.Length];
        Vector3[] newNormals = normals != null ? new Vector3[normals.Length] : null;
        Vector2[] newUVs = uvs != null ? new Vector2[uvs.Length] : null;
        Color[] newColors = colors != null ? new Color[colors.Length] : null;

        for (int i = 0; i < sortedVertices.Count; i++)
        {
            int oldIndex = sortedVertices[i];
            newVertices[i] = vertices[oldIndex];

            if (newNormals != null && oldIndex < normals.Length)
                newNormals[i] = normals[oldIndex];

            if (newUVs != null && oldIndex < uvs.Length)
                newUVs[i] = uvs[oldIndex];

            if (newColors != null && oldIndex < colors.Length)
                newColors[i] = colors[oldIndex];
        }

        // Cập nhật triangles với indices mới
        for (int i = 0; i < triangles.Length; i++)
        {
            triangles[i] = vertexMapping[triangles[i]];
        }

        // Cập nhật arrays
        vertices = newVertices;
        normals = newNormals;
        uvs = newUVs;
        colors = newColors;
    }

    /// <summary>
    /// Phân tích và báo cáo vertex optimization
    /// </summary>
    [ContextMenu("Phân Tích Vertices (Analyze Vertices)")]
    public void AnalyzeVertices()
    {
        MeshFilter[] meshFilters = GetComponentsInChildren<MeshFilter>();

        int totalVertices = 0;
        int totalTriangles = 0;
        int duplicateVertices = 0;
        int unusedVertices = 0;

        foreach (MeshFilter mf in meshFilters)
        {
            if (mf.sharedMesh == null) continue;

            Mesh mesh = mf.sharedMesh;
            Vector3[] vertices = mesh.vertices;
            int[] triangles = mesh.triangles;

            totalVertices += vertices.Length;
            totalTriangles += triangles.Length / 3;

            // Đếm duplicate vertices
            HashSet<Vector3> uniquePositions = new HashSet<Vector3>();
            foreach (Vector3 vertex in vertices)
            {
                Vector3 rounded = new Vector3(
                    Mathf.Round(vertex.x * 1000f) / 1000f,
                    Mathf.Round(vertex.y * 1000f) / 1000f,
                    Mathf.Round(vertex.z * 1000f) / 1000f
                );

                if (!uniquePositions.Add(rounded))
                {
                    duplicateVertices++;
                }
            }

            // Đếm unused vertices
            HashSet<int> usedVertices = new HashSet<int>();
            foreach (int vertexIndex in triangles)
            {
                usedVertices.Add(vertexIndex);
            }
            unusedVertices += vertices.Length - usedVertices.Count;
        }

        Debug.Log("=== PHÂN TÍCH VERTICES ===");
        Debug.Log($"Tổng vertices: {totalVertices:N0}");
        Debug.Log($"Tổng triangles: {totalTriangles:N0}");
        Debug.Log($"Duplicate vertices (ước tính): {duplicateVertices:N0}");
        Debug.Log($"Unused vertices: {unusedVertices:N0}");

        if (duplicateVertices > 0 || unusedVertices > 0)
        {
            float potentialReduction = (float)(duplicateVertices + unusedVertices) / totalVertices * 100f;
            Debug.Log($"Tiềm năng giảm vertices: {potentialReduction:F1}%");
            Debug.Log("💡 Bật 'Enable Vertex Welding' và 'Remove Unused Vertices' để tối ưu!");
        }
        else
        {
            Debug.Log("✅ Vertices đã được tối ưu tốt!");
        }
    }

    /// <summary>
    /// Ẩn mesh gốc sau khi gộp
    /// </summary>
    private void HideOriginalMeshes(CombineInstance[] combines)
    {
        if (settings.preserveOriginalMeshes) return;

        foreach (CombineInstance combine in combines)
        {
            // Tìm GameObject chứa mesh này
            MeshFilter[] allMeshFilters = GetComponentsInChildren<MeshFilter>();
            foreach (MeshFilter mf in allMeshFilters)
            {
                if (mf.sharedMesh == combine.mesh && mf.transform != transform)
                {
                    mf.gameObject.SetActive(false);
                    break;
                }
            }
        }
    }

    /// <summary>
    /// Dọn dẹp tối ưu hóa trước đó
    /// </summary>
    private void CleanupPreviousOptimization()
    {
        // Xóa combined objects
        foreach (GameObject obj in combinedObjects)
        {
            if (obj != null)
            {
                DestroyImmediate(obj);
            }
        }
        combinedObjects.Clear();

        // Dọn dẹp generated meshes
        CleanupGeneratedMeshes();

        // Hiện lại mesh gốc
        RestoreOriginalMeshes();

        // Clear cache
        materialGroups.Clear();
        spatialChunks.Clear();
    }

    /// <summary>
    /// Dọn dẹp generated meshes
    /// </summary>
    private void CleanupGeneratedMeshes()
    {
        foreach (Mesh mesh in generatedMeshes)
        {
            if (mesh != null)
            {
                DestroyImmediate(mesh);
            }
        }
        generatedMeshes.Clear();
    }

    /// <summary>
    /// Khôi phục mesh gốc
    /// </summary>
    private void RestoreOriginalMeshes()
    {
        MeshRenderer[] allRenderers = GetComponentsInChildren<MeshRenderer>(true);
        foreach (MeshRenderer renderer in allRenderers)
        {
            if (renderer.transform != transform && !renderer.gameObject.activeInHierarchy)
            {
                renderer.gameObject.SetActive(true);
            }
        }
    }

    /// <summary>
    /// Thống kê tối ưu hóa
    /// </summary>
    [ContextMenu("Thống Kê Tối Ưu (Optimization Stats)")]
    public void PrintOptimizationStats()
    {
        MeshRenderer[] originalRenderers = GetComponentsInChildren<MeshRenderer>(true);
        int originalMeshCount = 0;
        int originalVertexCount = 0;
        int originalTriangleCount = 0;

        foreach (MeshRenderer renderer in originalRenderers)
        {
            if (renderer.transform == transform) continue;

            MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                originalMeshCount++;
                originalVertexCount += meshFilter.sharedMesh.vertexCount;
                originalTriangleCount += meshFilter.sharedMesh.triangles.Length / 3;
            }
        }

        int optimizedVertexCount = 0;
        int optimizedTriangleCount = 0;

        foreach (Mesh mesh in generatedMeshes)
        {
            if (mesh != null)
            {
                optimizedVertexCount += mesh.vertexCount;
                optimizedTriangleCount += mesh.triangles.Length / 3;
            }
        }

        Debug.Log("=== THỐNG KÊ TỐI ƯU HÓA MESH ===");
        Debug.Log($"Mesh gốc: {originalMeshCount} objects");
        Debug.Log($"Mesh sau tối ưu: {combinedObjects.Count} objects");
        Debug.Log($"Vertices gốc: {originalVertexCount:N0}");
        Debug.Log($"Vertices sau tối ưu: {optimizedVertexCount:N0}");
        Debug.Log($"Triangles gốc: {originalTriangleCount:N0}");
        Debug.Log($"Triangles sau tối ưu: {optimizedTriangleCount:N0}");

        if (originalMeshCount > 0)
        {
            float meshReduction = (1f - (float)combinedObjects.Count / originalMeshCount) * 100f;
            Debug.Log($"Giảm số mesh: {meshReduction:F1}%");
        }

        if (originalVertexCount > 0)
        {
            float vertexReduction = (1f - (float)optimizedVertexCount / originalVertexCount) * 100f;
            Debug.Log($"Giảm vertices: {vertexReduction:F1}%");
        }
    }

    /// <summary>
    /// Khôi phục về trạng thái ban đầu
    /// </summary>
    [ContextMenu("Khôi Phục Ban Đầu (Restore Original)")]
    public void RestoreToOriginal()
    {
        CleanupPreviousOptimization();
        Debug.Log("✅ Đã khôi phục về trạng thái ban đầu!");
    }

    /// <summary>
    /// Kiểm tra xem có thể tối ưu hóa không
    /// </summary>
    public bool CanOptimize()
    {
        MeshRenderer[] renderers = GetComponentsInChildren<MeshRenderer>();
        int validMeshCount = 0;

        foreach (MeshRenderer renderer in renderers)
        {
            if (renderer.transform == transform) continue;

            MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                validMeshCount++;
            }
        }

        return validMeshCount > 1;
    }

    /// <summary>
    /// Tự động tối ưu hóa khi có thay đổi trong Editor
    /// </summary>
    private void OnValidate()
    {
        if (Application.isPlaying) return;

        // Validate settings
        settings.maxVerticesPerMesh = Mathf.Clamp(settings.maxVerticesPerMesh, 1000, 65000);
        settings.lod1Quality = Mathf.Clamp01(settings.lod1Quality);
        settings.lod2Quality = Mathf.Clamp01(settings.lod2Quality);
        settings.lod3Quality = Mathf.Clamp01(settings.lod3Quality);
        settings.cullDistance = Mathf.Max(1f, settings.cullDistance);

        // Ensure LOD qualities are in descending order
        if (settings.lod2Quality > settings.lod1Quality)
            settings.lod2Quality = settings.lod1Quality;
        if (settings.lod3Quality > settings.lod2Quality)
            settings.lod3Quality = settings.lod2Quality;
    }
}
