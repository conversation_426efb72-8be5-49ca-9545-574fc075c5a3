using UnityEngine;

namespace Tilemap3D.Core
{
    /// <summary>
    /// ScriptableObject định nghĩa một tile asset với các thuộc tính và metadata
    /// </summary>
    [CreateAssetMenu(fileName = "New Tile Asset", menuName = "Tilemap3D/Tile Asset")]
    public class TileAsset : ScriptableObject
    {
        [Header("Basic Properties")]
        [SerializeField] private string tileName;
        [SerializeField] private GameObject prefab;
        [SerializeField] private Sprite icon;
        [SerializeField] private string category = "Default";
        
        [Header("Tile Properties")]
        [SerializeField] private TileType tileType = TileType.Static;
        [SerializeField] private bool isWalkable = true;
        [SerializeField] private bool hasCollider = true;
        [SerializeField] private float height = 1f;
        
        [Header("Visual Properties")]
        [SerializeField] private Material[] materials;
        [SerializeField] private Color tintColor = Color.white;
        [SerializeField] private Vector3 scale = Vector3.one;
        [SerializeField] private Vector3 rotation = Vector3.zero;
        
        [Header("Auto-Tiling")]
        [SerializeField] private bool supportsAutoTiling = false;
        [SerializeField] private AutoTileRule[] autoTileRules;
        
        [Header("Animation")]
        [SerializeField] private bool isAnimated = false;
        [SerializeField] private GameObject[] animationFrames;
        [SerializeField] private float animationSpeed = 1f;
        
        [Header("Physics")]
        [SerializeField] private PhysicsMaterial physicMaterial;
        [SerializeField] private LayerMask collisionLayer = 1;
        
        [Header("Audio")]
        [SerializeField] private AudioClip placementSound;
        [SerializeField] private AudioClip destructionSound;
        
        // Properties
        public string TileName => string.IsNullOrEmpty(tileName) ? name : tileName;
        public GameObject Prefab => prefab;
        public Sprite Icon => icon;
        public string Category => category;
        public TileType TileType => tileType;
        public bool IsWalkable => isWalkable;
        public bool HasCollider => hasCollider;
        public float Height => height;
        public Material[] Materials => materials;
        public Color TintColor => tintColor;
        public Vector3 Scale => scale;
        public Vector3 Rotation => rotation;
        public bool SupportsAutoTiling => supportsAutoTiling;
        public AutoTileRule[] AutoTileRules => autoTileRules;
        public bool IsAnimated => isAnimated;
        public GameObject[] AnimationFrames => animationFrames;
        public float AnimationSpeed => animationSpeed;
        public PhysicsMaterial PhysicMaterial => physicMaterial;
        public LayerMask CollisionLayer => collisionLayer;
        public AudioClip PlacementSound => placementSound;
        public AudioClip DestructionSound => destructionSound;
        
        /// <summary>
        /// Tạo instance của tile tại vị trí chỉ định
        /// </summary>
        public GameObject CreateInstance(Vector3 position, Quaternion rotation, Transform parent = null)
        {
            if (prefab == null) return null;
            
            GameObject instance = Instantiate(prefab, position, rotation, parent);
            
            // Apply tint color
            if (tintColor != Color.white)
            {
                var renderers = instance.GetComponentsInChildren<Renderer>();
                foreach (var renderer in renderers)
                {
                    var materials = renderer.materials;
                    for (int i = 0; i < materials.Length; i++)
                    {
                        materials[i].color = tintColor;
                    }
                    renderer.materials = materials;
                }
            }
            
            // Apply scale
            if (scale != Vector3.one)
            {
                instance.transform.localScale = scale;
            }
            
            // Apply rotation offset
            if (this.rotation != Vector3.zero)
            {
                instance.transform.Rotate(this.rotation);
            }
            
            // Setup collider
            if (!hasCollider)
            {
                var colliders = instance.GetComponentsInChildren<Collider>();
                foreach (var collider in colliders)
                {
                    collider.enabled = false;
                }
            }
            else if (physicMaterial != null)
            {
                var colliders = instance.GetComponentsInChildren<Collider>();
                foreach (var collider in colliders)
                {
                    collider.material = physicMaterial;
                }
            }
            
            // Add tile component
            var tileComponent = instance.GetComponent<TileInstance>();
            if (tileComponent == null)
            {
                tileComponent = instance.AddComponent<TileInstance>();
            }
            tileComponent.Initialize(this);
            
            return instance;
        }
        
        /// <summary>
        /// Validate tile asset
        /// </summary>
        private void OnValidate()
        {
            if (prefab == null)
            {
                Debug.LogWarning($"Tile Asset '{name}' has no prefab assigned!", this);
            }
            
            if (isAnimated && (animationFrames == null || animationFrames.Length == 0))
            {
                Debug.LogWarning($"Animated tile '{name}' has no animation frames!", this);
                isAnimated = false;
            }
            
            if (supportsAutoTiling && (autoTileRules == null || autoTileRules.Length == 0))
            {
                Debug.LogWarning($"Auto-tiling tile '{name}' has no auto-tile rules!", this);
                supportsAutoTiling = false;
            }
        }
    }
    
    /// <summary>
    /// Enum định nghĩa các loại tile
    /// </summary>
    public enum TileType
    {
        Static,      // Tile tĩnh thông thường
        Animated,    // Tile có animation
        Interactive, // Tile có thể tương tác
        Destructible,// Tile có thể phá hủy
        AutoTile,    // Tile tự động kết nối
        Liquid,      // Tile chất lỏng
        Decoration   // Tile trang trí
    }
    
    /// <summary>
    /// Rule cho auto-tiling
    /// </summary>
    [System.Serializable]
    public class AutoTileRule
    {
        public string ruleName;
        public GameObject prefabVariant;
        public TileNeighborPattern pattern;
        public int priority = 0;
    }
    
    /// <summary>
    /// Pattern cho việc kiểm tra neighbor tiles
    /// </summary>
    [System.Serializable]
    public class TileNeighborPattern
    {
        public NeighborState[] neighbors = new NeighborState[8]; // 8 directions around tile
        
        public enum NeighborState
        {
            Any,        // Không quan tâm
            Empty,      // Phải trống
            SameTile,   // Phải cùng loại tile
            DifferentTile, // Phải khác loại tile
            SameCategory   // Phải cùng category
        }
    }
}
