using UnityEngine;
using System.Collections.Generic;

namespace Tilemap3D.Core
{
    /// <summary>
    /// Represents a chunk of tiles for performance optimization
    /// </summary>
    public class TileChunk
    {
        private Vector3Int chunkPosition;
        private Vector3Int chunkSize;
        private GameObject chunkObject;
        private List<TileInstance> tiles = new List<TileInstance>();
        private bool isVisible = true;
        private bool isDirty = true;
        
        // Properties
        public Vector3Int ChunkPosition => chunkPosition;
        public Vector3Int ChunkSize => chunkSize;
        public Vector3 WorldCenter { get; private set; }
        public List<TileInstance> Tiles => tiles;
        public bool IsVisible => isVisible;
        public bool IsDirty => isDirty;
        
        /// <summary>
        /// Constructor
        /// </summary>
        public TileChunk(Vector3Int position, Vector3Int size)
        {
            chunkPosition = position;
            chunkSize = size;
            
            // Calculate world center
            Vector3 chunkWorldPos = new Vector3(
                position.x * size.x,
                position.y * size.y,
                position.z * size.z
            );
            
            Vector3 chunkWorldSize = new Vector3(size.x, size.y, size.z);
            WorldCenter = chunkWorldPos + chunkWorldSize * 0.5f;
        }
        
        /// <summary>
        /// Update chunk with tiles from tilemap
        /// </summary>
        public void UpdateChunk(Tilemap3D tilemap)
        {
            if (!isDirty) return;
            
            tiles.Clear();
            
            // Calculate chunk bounds in grid space
            Vector3Int minPos = new Vector3Int(
                chunkPosition.x * chunkSize.x,
                chunkPosition.y * chunkSize.y,
                chunkPosition.z * chunkSize.z
            );
            
            Vector3Int maxPos = minPos + chunkSize;
            
            // Collect all tiles in this chunk
            foreach (var layer in tilemap.Layers)
            {
                foreach (var kvp in layer.Tiles)
                {
                    Vector3Int tilePos = kvp.Key;
                    
                    if (tilePos.x >= minPos.x && tilePos.x < maxPos.x &&
                        tilePos.y >= minPos.y && tilePos.y < maxPos.y &&
                        tilePos.z >= minPos.z && tilePos.z < maxPos.z)
                    {
                        tiles.Add(kvp.Value);
                    }
                }
            }
            
            isDirty = false;
        }
        
        /// <summary>
        /// Set chunk visibility
        /// </summary>
        public void SetVisible(bool visible)
        {
            if (isVisible == visible) return;
            
            isVisible = visible;
            
            foreach (var tile in tiles)
            {
                if (tile != null && tile.gameObject != null)
                {
                    tile.gameObject.SetActive(visible);
                }
            }
        }
        
        /// <summary>
        /// Mark chunk as dirty
        /// </summary>
        public void MarkDirty()
        {
            isDirty = true;
        }
        
        /// <summary>
        /// Get chunk bounds
        /// </summary>
        public Bounds GetBounds()
        {
            Vector3 chunkWorldPos = new Vector3(
                chunkPosition.x * chunkSize.x,
                chunkPosition.y * chunkSize.y,
                chunkPosition.z * chunkSize.z
            );
            
            Vector3 chunkWorldSize = new Vector3(chunkSize.x, chunkSize.y, chunkSize.z);
            
            return new Bounds(chunkWorldPos + chunkWorldSize * 0.5f, chunkWorldSize);
        }
    }
}
