using UnityEngine;

namespace Tilemap3D.Core
{
    /// <summary>
    /// Component gắn vào mỗi tile instance trong scene
    /// </summary>
    public class TileInstance : MonoBehaviour
    {
        [SerializeField] private TileAsset tileAsset;
        [SerializeField] private Vector3Int gridPosition;
        [SerializeField] private int layerIndex;
        [SerializeField] private float placementTime;
        
        // Animation
        private int currentFrame = 0;
        private float animationTimer = 0f;
        private GameObject[] frameInstances;
        
        // Events
        public System.Action<TileInstance> OnDestroyed;
        public System.Action<TileInstance> OnInteracted;
        
        // Properties
        public TileAsset TileAsset => tileAsset;
        public Vector3Int GridPosition => gridPosition;
        public int LayerIndex => layerIndex;
        public float PlacementTime => placementTime;
        
        /// <summary>
        /// Initialize tile instance
        /// </summary>
        public void Initialize(TileAsset asset, Vector3Int gridPos = default, int layer = 0)
        {
            tileAsset = asset;
            gridPosition = gridPos;
            layerIndex = layer;
            placementTime = Time.time;
            
            if (asset.IsAnimated)
            {
                SetupAnimation();
            }
            
            // Add audio source if needed
            if (asset.PlacementSound != null || asset.DestructionSound != null)
            {
                var audioSource = GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = gameObject.AddComponent<AudioSource>();
                    audioSource.playOnAwake = false;
                    audioSource.spatialBlend = 1f; // 3D sound
                }
            }
            
            // Play placement sound
            if (asset.PlacementSound != null)
            {
                PlaySound(asset.PlacementSound);
            }
        }
        
        /// <summary>
        /// Setup animation for animated tiles
        /// </summary>
        private void SetupAnimation()
        {
            if (!tileAsset.IsAnimated || tileAsset.AnimationFrames == null) return;
            
            frameInstances = new GameObject[tileAsset.AnimationFrames.Length];
            
            for (int i = 0; i < tileAsset.AnimationFrames.Length; i++)
            {
                if (tileAsset.AnimationFrames[i] != null)
                {
                    frameInstances[i] = Instantiate(tileAsset.AnimationFrames[i], transform);
                    frameInstances[i].SetActive(i == 0);
                }
            }
            
            // Hide original prefab renderers
            var renderers = GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                if (renderer.transform == transform)
                {
                    renderer.enabled = false;
                }
            }
        }
        
        /// <summary>
        /// Update animation
        /// </summary>
        private void Update()
        {
            if (tileAsset != null && tileAsset.IsAnimated && frameInstances != null)
            {
                UpdateAnimation();
            }
        }
        
        /// <summary>
        /// Update animation frames
        /// </summary>
        private void UpdateAnimation()
        {
            animationTimer += Time.deltaTime * tileAsset.AnimationSpeed;
            
            if (animationTimer >= 1f)
            {
                animationTimer = 0f;
                
                // Hide current frame
                if (frameInstances[currentFrame] != null)
                {
                    frameInstances[currentFrame].SetActive(false);
                }
                
                // Show next frame
                currentFrame = (currentFrame + 1) % frameInstances.Length;
                if (frameInstances[currentFrame] != null)
                {
                    frameInstances[currentFrame].SetActive(true);
                }
            }
        }
        
        /// <summary>
        /// Handle interaction
        /// </summary>
        public void Interact()
        {
            OnInteracted?.Invoke(this);
            
            // Custom interaction logic based on tile type
            switch (tileAsset.TileType)
            {
                case TileType.Interactive:
                    HandleInteractiveTile();
                    break;
                case TileType.Destructible:
                    HandleDestructibleTile();
                    break;
            }
        }
        
        /// <summary>
        /// Handle interactive tile logic
        /// </summary>
        private void HandleInteractiveTile()
        {
            // Override in derived classes or use events
            Debug.Log($"Interacted with {tileAsset.TileName} at {gridPosition}");
        }
        
        /// <summary>
        /// Handle destructible tile logic
        /// </summary>
        private void HandleDestructibleTile()
        {
            DestroyTile();
        }
        
        /// <summary>
        /// Destroy this tile
        /// </summary>
        public void DestroyTile()
        {
            // Play destruction sound
            if (tileAsset.DestructionSound != null)
            {
                PlaySound(tileAsset.DestructionSound);
            }
            
            OnDestroyed?.Invoke(this);
            
            // Add destruction effects here
            CreateDestructionEffect();
            
            Destroy(gameObject);
        }
        
        /// <summary>
        /// Create destruction effect
        /// </summary>
        private void CreateDestructionEffect()
        {
            // Simple particle effect
            var particles = new GameObject("DestructionEffect");
            particles.transform.position = transform.position;
            
            var particleSystem = particles.AddComponent<ParticleSystem>();
            var main = particleSystem.main;
            main.startLifetime = 1f;
            main.startSpeed = 5f;
            main.maxParticles = 20;
            
            var emission = particleSystem.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0f, 20)
            });
            
            Destroy(particles, 2f);
        }
        
        /// <summary>
        /// Play sound effect
        /// </summary>
        private void PlaySound(AudioClip clip)
        {
            var audioSource = GetComponent<AudioSource>();
            if (audioSource != null && clip != null)
            {
                audioSource.clip = clip;
                audioSource.Play();
            }
        }
        
        /// <summary>
        /// Get tile data for serialization
        /// </summary>
        public TileData GetTileData()
        {
            return new TileData
            {
                tileAssetGUID = tileAsset != null ? GetAssetGUID(tileAsset) : "",
                gridPosition = gridPosition,
                layerIndex = layerIndex,
                rotation = transform.rotation,
                scale = transform.localScale,
                placementTime = placementTime
            };
        }
        
        /// <summary>
        /// Get asset GUID for serialization
        /// </summary>
        private string GetAssetGUID(TileAsset asset)
        {
#if UNITY_EDITOR
            return UnityEditor.AssetDatabase.AssetPathToGUID(UnityEditor.AssetDatabase.GetAssetPath(asset));
#else
            return asset.name; // Fallback for runtime
#endif
        }
        
        /// <summary>
        /// Validate tile instance
        /// </summary>
        private void OnValidate()
        {
            if (tileAsset == null)
            {
                Debug.LogWarning($"TileInstance on {gameObject.name} has no TileAsset assigned!", this);
            }
        }
        
        /// <summary>
        /// Draw gizmos for debugging
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireCube(transform.position, Vector3.one);
            
            // Draw grid position
            Gizmos.color = Color.red;
            Gizmos.DrawWireCube(new Vector3(gridPosition.x, gridPosition.y, gridPosition.z), Vector3.one * 0.1f);
        }
    }
    
    /// <summary>
    /// Struct chứa data của tile để serialization
    /// </summary>
    [System.Serializable]
    public struct TileData
    {
        public string tileAssetGUID;
        public Vector3Int gridPosition;
        public int layerIndex;
        public Quaternion rotation;
        public Vector3 scale;
        public float placementTime;
    }
}
