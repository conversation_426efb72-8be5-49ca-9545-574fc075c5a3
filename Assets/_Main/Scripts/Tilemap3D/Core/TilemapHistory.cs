using System.Collections.Generic;
using UnityEngine;

namespace Tilemap3D.Core
{
    /// <summary>
    /// Manages undo/redo history for tilemap operations
    /// </summary>
    public class TilemapHistory
    {
        private List<TileChangeData> undoStack = new List<TileChangeData>();
        private List<TileChangeData> redoStack = new List<TileChangeData>();
        private int maxHistorySize = 100;
        private Tilemap3D tilemap;
        
        // Properties
        public bool CanUndo => undoStack.Count > 0;
        public bool CanRedo => redoStack.Count > 0;
        public int UndoCount => undoStack.Count;
        public int RedoCount => redoStack.Count;
        
        /// <summary>
        /// Initialize history system
        /// </summary>
        public void Initialize(Tilemap3D tilemapInstance)
        {
            tilemap = tilemapInstance;
        }
        
        /// <summary>
        /// Record a change for undo/redo
        /// </summary>
        public void RecordChange(TileChangeData changeData)
        {
            undoStack.Add(changeData);
            
            // Clear redo stack when new action is performed
            redoStack.Clear();
            
            // Limit history size
            if (undoStack.Count > maxHistorySize)
            {
                undoStack.RemoveAt(0);
            }
        }
        
        /// <summary>
        /// Record multiple changes as a batch
        /// </summary>
        public void RecordBatchChanges(List<TileChangeData> changes)
        {
            var batchData = new TileChangeData
            {
                changeType = TileChangeType.Batch,
                batchChanges = new List<TileChangeData>(changes)
            };
            
            RecordChange(batchData);
        }
        
        /// <summary>
        /// Undo last action
        /// </summary>
        public void Undo()
        {
            if (!CanUndo) return;
            
            var changeData = undoStack[undoStack.Count - 1];
            undoStack.RemoveAt(undoStack.Count - 1);
            
            // Apply reverse of the change
            ApplyReverseChange(changeData);
            
            // Add to redo stack
            redoStack.Add(changeData);
        }
        
        /// <summary>
        /// Redo last undone action
        /// </summary>
        public void Redo()
        {
            if (!CanRedo) return;
            
            var changeData = redoStack[redoStack.Count - 1];
            redoStack.RemoveAt(redoStack.Count - 1);
            
            // Apply the change
            ApplyChange(changeData);
            
            // Add back to undo stack
            undoStack.Add(changeData);
        }
        
        /// <summary>
        /// Apply a change
        /// </summary>
        private void ApplyChange(TileChangeData changeData)
        {
            switch (changeData.changeType)
            {
                case TileChangeType.Place:
                    if (changeData.newTileAsset != null)
                    {
                        tilemap.PlaceTile(changeData.position, changeData.newTileAsset, changeData.layerIndex);
                    }
                    break;
                    
                case TileChangeType.Remove:
                    tilemap.RemoveTile(changeData.position, changeData.layerIndex);
                    break;
                    
                case TileChangeType.Replace:
                    if (changeData.newTileAsset != null)
                    {
                        tilemap.PlaceTile(changeData.position, changeData.newTileAsset, changeData.layerIndex);
                    }
                    else
                    {
                        tilemap.RemoveTile(changeData.position, changeData.layerIndex);
                    }
                    break;
                    
                case TileChangeType.Batch:
                    if (changeData.batchChanges != null)
                    {
                        foreach (var batchChange in changeData.batchChanges)
                        {
                            ApplyChange(batchChange);
                        }
                    }
                    break;
            }
        }
        
        /// <summary>
        /// Apply reverse of a change
        /// </summary>
        private void ApplyReverseChange(TileChangeData changeData)
        {
            switch (changeData.changeType)
            {
                case TileChangeType.Place:
                    if (changeData.oldTileAsset != null)
                    {
                        tilemap.PlaceTile(changeData.position, changeData.oldTileAsset, changeData.layerIndex);
                    }
                    else
                    {
                        tilemap.RemoveTile(changeData.position, changeData.layerIndex);
                    }
                    break;
                    
                case TileChangeType.Remove:
                    if (changeData.oldTileAsset != null)
                    {
                        tilemap.PlaceTile(changeData.position, changeData.oldTileAsset, changeData.layerIndex);
                    }
                    break;
                    
                case TileChangeType.Replace:
                    if (changeData.oldTileAsset != null)
                    {
                        tilemap.PlaceTile(changeData.position, changeData.oldTileAsset, changeData.layerIndex);
                    }
                    else
                    {
                        tilemap.RemoveTile(changeData.position, changeData.layerIndex);
                    }
                    break;
                    
                case TileChangeType.Batch:
                    if (changeData.batchChanges != null)
                    {
                        // Apply batch changes in reverse order
                        for (int i = changeData.batchChanges.Count - 1; i >= 0; i--)
                        {
                            ApplyReverseChange(changeData.batchChanges[i]);
                        }
                    }
                    break;
            }
        }
        
        /// <summary>
        /// Clear all history
        /// </summary>
        public void ClearHistory()
        {
            undoStack.Clear();
            redoStack.Clear();
        }
        
        /// <summary>
        /// Set maximum history size
        /// </summary>
        public void SetMaxHistorySize(int size)
        {
            maxHistorySize = Mathf.Max(1, size);
            
            // Trim existing history if needed
            while (undoStack.Count > maxHistorySize)
            {
                undoStack.RemoveAt(0);
            }
        }
        
        /// <summary>
        /// Get history info for debugging
        /// </summary>
        public string GetHistoryInfo()
        {
            return $"Undo: {undoStack.Count}, Redo: {redoStack.Count}, Max: {maxHistorySize}";
        }
    }
    
    /// <summary>
    /// Data structure for tile changes
    /// </summary>
    [System.Serializable]
    public class TileChangeData
    {
        public Vector3Int position;
        public int layerIndex;
        public TileAsset oldTileAsset;
        public TileAsset newTileAsset;
        public TileChangeType changeType;
        public List<TileChangeData> batchChanges; // For batch operations
        public float timestamp;
        
        public TileChangeData()
        {
            timestamp = Time.time;
        }
    }
    
    /// <summary>
    /// Types of tile changes
    /// </summary>
    public enum TileChangeType
    {
        Place,
        Remove,
        Replace,
        Batch
    }
}
