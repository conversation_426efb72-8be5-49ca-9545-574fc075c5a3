using System.Collections.Generic;
using UnityEngine;

namespace Tilemap3D.Core
{
    /// <summary>
    /// Represents a single layer in the tilemap
    /// </summary>
    [System.Serializable]
    public class TilemapLayer
    {
        [SerializeField] private string layerName = "Layer";
        [SerializeField] private bool isVisible = true;
        [SerializeField] private bool isLocked = false;
        [SerializeField] private float opacity = 1f;
        [SerializeField] private Color tintColor = Color.white;
        [SerializeField] private BlendMode blendMode = BlendMode.Normal;
        [SerializeField] private int sortingOrder = 0;
        [SerializeField] private float heightOffset = 0f;
        
        // Runtime data
        private Dictionary<Vector3Int, TileInstance> tiles = new Dictionary<Vector3Int, TileInstance>();
        private Transform layerTransform;
        private bool isDirty = false;
        
        // Events
        public System.Action<TilemapLayer> OnLayerChanged;
        public System.Action<Vector3Int, TileInstance> OnTileAdded;
        public System.Action<Vector3Int, TileInstance> OnTileRemoved;
        
        // Properties
        public string LayerName 
        { 
            get => layerName; 
            set 
            { 
                layerName = value;
                if (layerTransform != null) layerTransform.name = value;
                MarkDirty();
            } 
        }
        
        public bool IsVisible 
        { 
            get => isVisible; 
            set 
            { 
                isVisible = value;
                UpdateVisibility();
                MarkDirty();
            } 
        }
        
        public bool IsLocked 
        { 
            get => isLocked; 
            set 
            { 
                isLocked = value;
                MarkDirty();
            } 
        }
        
        public float Opacity 
        { 
            get => opacity; 
            set 
            { 
                opacity = Mathf.Clamp01(value);
                UpdateOpacity();
                MarkDirty();
            } 
        }
        
        public Color TintColor 
        { 
            get => tintColor; 
            set 
            { 
                tintColor = value;
                UpdateTintColor();
                MarkDirty();
            } 
        }
        
        public BlendMode BlendMode 
        { 
            get => blendMode; 
            set 
            { 
                blendMode = value;
                UpdateBlendMode();
                MarkDirty();
            } 
        }
        
        public int SortingOrder 
        { 
            get => sortingOrder; 
            set 
            { 
                sortingOrder = value;
                UpdateSortingOrder();
                MarkDirty();
            } 
        }
        
        public float HeightOffset 
        { 
            get => heightOffset; 
            set 
            { 
                heightOffset = value;
                UpdateHeightOffset();
                MarkDirty();
            } 
        }
        
        public Transform LayerTransform => layerTransform;
        public Dictionary<Vector3Int, TileInstance> Tiles => tiles;
        public int TileCount => tiles.Count;
        public bool IsDirty => isDirty;
        
        /// <summary>
        /// Initialize layer with transform
        /// </summary>
        public void Initialize(Transform parent, int layerIndex)
        {
            if (layerTransform == null)
            {
                var layerObject = new GameObject(layerName);
                layerTransform = layerObject.transform;
                layerTransform.SetParent(parent);
                layerTransform.localPosition = Vector3.up * (layerIndex * 0.01f + heightOffset);
            }
            
            UpdateLayerProperties();
        }
        
        /// <summary>
        /// Add tile to layer
        /// </summary>
        public bool AddTile(Vector3Int position, TileAsset tileAsset)
        {
            if (isLocked || tiles.ContainsKey(position)) return false;
            
            Vector3 worldPosition = new Vector3(position.x, position.y + heightOffset, position.z);
            GameObject tileObject = tileAsset.CreateInstance(worldPosition, Quaternion.identity, layerTransform);
            
            if (tileObject != null)
            {
                var tileInstance = tileObject.GetComponent<TileInstance>();
                if (tileInstance != null)
                {
                    tileInstance.Initialize(tileAsset, position, sortingOrder);
                    tiles[position] = tileInstance;
                    
                    // Apply layer properties to tile
                    ApplyLayerPropertiesToTile(tileInstance);
                    
                    OnTileAdded?.Invoke(position, tileInstance);
                    MarkDirty();
                    return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// Remove tile from layer
        /// </summary>
        public bool RemoveTile(Vector3Int position)
        {
            if (isLocked || !tiles.TryGetValue(position, out TileInstance tile)) return false;
            
            tiles.Remove(position);
            OnTileRemoved?.Invoke(position, tile);
            
            if (tile != null)
            {
                Object.DestroyImmediate(tile.gameObject);
            }
            
            MarkDirty();
            return true;
        }
        
        /// <summary>
        /// Get tile at position
        /// </summary>
        public TileInstance GetTile(Vector3Int position)
        {
            tiles.TryGetValue(position, out TileInstance tile);
            return tile;
        }
        
        /// <summary>
        /// Check if position has tile
        /// </summary>
        public bool HasTile(Vector3Int position)
        {
            return tiles.ContainsKey(position);
        }
        
        /// <summary>
        /// Clear all tiles in layer
        /// </summary>
        public void ClearAllTiles()
        {
            if (isLocked) return;
            
            var tilesToRemove = new List<Vector3Int>(tiles.Keys);
            foreach (var position in tilesToRemove)
            {
                RemoveTile(position);
            }
        }
        
        /// <summary>
        /// Update layer visibility
        /// </summary>
        private void UpdateVisibility()
        {
            if (layerTransform != null)
            {
                layerTransform.gameObject.SetActive(isVisible);
            }
        }
        
        /// <summary>
        /// Update layer opacity
        /// </summary>
        private void UpdateOpacity()
        {
            foreach (var tile in tiles.Values)
            {
                if (tile != null)
                {
                    ApplyOpacityToTile(tile);
                }
            }
        }
        
        /// <summary>
        /// Update layer tint color
        /// </summary>
        private void UpdateTintColor()
        {
            foreach (var tile in tiles.Values)
            {
                if (tile != null)
                {
                    ApplyTintColorToTile(tile);
                }
            }
        }
        
        /// <summary>
        /// Update blend mode
        /// </summary>
        private void UpdateBlendMode()
        {
            foreach (var tile in tiles.Values)
            {
                if (tile != null)
                {
                    ApplyBlendModeToTile(tile);
                }
            }
        }
        
        /// <summary>
        /// Update sorting order
        /// </summary>
        private void UpdateSortingOrder()
        {
            foreach (var tile in tiles.Values)
            {
                if (tile != null)
                {
                    ApplySortingOrderToTile(tile);
                }
            }
        }
        
        /// <summary>
        /// Update height offset
        /// </summary>
        private void UpdateHeightOffset()
        {
            if (layerTransform != null)
            {
                Vector3 pos = layerTransform.localPosition;
                pos.y = heightOffset;
                layerTransform.localPosition = pos;
            }
        }
        
        /// <summary>
        /// Apply all layer properties to a tile
        /// </summary>
        private void ApplyLayerPropertiesToTile(TileInstance tile)
        {
            ApplyOpacityToTile(tile);
            ApplyTintColorToTile(tile);
            ApplyBlendModeToTile(tile);
            ApplySortingOrderToTile(tile);
        }
        
        /// <summary>
        /// Apply opacity to tile
        /// </summary>
        private void ApplyOpacityToTile(TileInstance tile)
        {
            var renderers = tile.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                foreach (var material in renderer.materials)
                {
                    if (material.HasProperty("_Color"))
                    {
                        Color color = material.color;
                        color.a = opacity;
                        material.color = color;
                    }
                }
            }
        }
        
        /// <summary>
        /// Apply tint color to tile
        /// </summary>
        private void ApplyTintColorToTile(TileInstance tile)
        {
            var renderers = tile.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                foreach (var material in renderer.materials)
                {
                    if (material.HasProperty("_Color"))
                    {
                        material.color = tintColor;
                    }
                }
            }
        }
        
        /// <summary>
        /// Apply blend mode to tile
        /// </summary>
        private void ApplyBlendModeToTile(TileInstance tile)
        {
            // Implementation depends on shader support
            // This is a simplified version
        }
        
        /// <summary>
        /// Apply sorting order to tile
        /// </summary>
        private void ApplySortingOrderToTile(TileInstance tile)
        {
            var renderers = tile.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                renderer.sortingOrder = sortingOrder;
            }
        }
        
        /// <summary>
        /// Update all layer properties
        /// </summary>
        private void UpdateLayerProperties()
        {
            UpdateVisibility();
            UpdateOpacity();
            UpdateTintColor();
            UpdateBlendMode();
            UpdateSortingOrder();
            UpdateHeightOffset();
        }
        
        /// <summary>
        /// Mark layer as dirty
        /// </summary>
        private void MarkDirty()
        {
            isDirty = true;
            OnLayerChanged?.Invoke(this);
        }
        
        /// <summary>
        /// Clear dirty flag
        /// </summary>
        public void ClearDirtyFlag()
        {
            isDirty = false;
        }
        
        /// <summary>
        /// Get all tile positions
        /// </summary>
        public Vector3Int[] GetAllTilePositions()
        {
            var positions = new Vector3Int[tiles.Count];
            tiles.Keys.CopyTo(positions, 0);
            return positions;
        }
        
        /// <summary>
        /// Get layer bounds
        /// </summary>
        public Bounds GetBounds()
        {
            if (tiles.Count == 0) return new Bounds();
            
            Vector3 min = Vector3.positiveInfinity;
            Vector3 max = Vector3.negativeInfinity;
            
            foreach (var position in tiles.Keys)
            {
                min = Vector3.Min(min, position);
                max = Vector3.Max(max, position);
            }
            
            Vector3 center = (min + max) * 0.5f;
            Vector3 size = max - min + Vector3.one;
            
            return new Bounds(center, size);
        }
    }
    
    /// <summary>
    /// Blend modes for layers
    /// </summary>
    public enum BlendMode
    {
        Normal,
        Multiply,
        Screen,
        Overlay,
        SoftLight,
        HardLight,
        ColorDodge,
        ColorBurn,
        Darken,
        Lighten,
        Difference,
        Exclusion
    }
}
