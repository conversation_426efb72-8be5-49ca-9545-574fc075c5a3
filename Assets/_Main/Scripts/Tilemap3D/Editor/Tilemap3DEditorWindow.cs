using UnityEditor;
using UnityEngine;
using Tilemap3D.Core;
using Tilemap3D.Tools;

namespace Tilemap3D.Editor
{
    /// <summary>
    /// Standalone editor window for Tilemap3D
    /// </summary>
    public class Tilemap3DEditorWindow : EditorWindow
    {
        private Tilemap3D selectedTilemap;
        private BrushManager brushManager;
        private Vector2 scrollPosition;
        private Vector2 tileScrollPosition;
        private Vector2 layerScrollPosition;
        
        // UI State
        private bool showTileLibrary = true;
        private bool showLayers = true;
        private bool showBrushes = true;
        private bool showSettings = true;
        private string searchFilter = "";
        
        // Constants
        private const int TILE_SIZE = 80;
        private const int LAYER_HEIGHT = 30;
        
        [MenuItem("Window/Tilemap3D/Editor Window")]
        public static void ShowWindow()
        {
            var window = GetWindow<Tilemap3DEditorWindow>("Tilemap3D Editor");
            window.minSize = new Vector2(400, 600);
            window.Show();
        }
        
        private void OnEnable()
        {
            // Initialize brush manager
            if (brushManager == null)
            {
                brushManager = new BrushManager();
                brushManager.Initialize();
            }
            
            // Find tilemap in selection
            RefreshSelectedTilemap();
            
            Selection.selectionChanged += RefreshSelectedTilemap;
        }
        
        private void OnDisable()
        {
            Selection.selectionChanged -= RefreshSelectedTilemap;
        }
        
        private void OnGUI()
        {
            DrawHeader();
            
            if (selectedTilemap == null)
            {
                DrawNoTilemapSelected();
                return;
            }
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            DrawTilemapInfo();
            DrawBrushPanel();
            DrawTileLibrary();
            DrawLayerPanel();
            DrawSettingsPanel();
            DrawQuickActions();
            
            EditorGUILayout.EndScrollView();
        }
        
        /// <summary>
        /// Draw window header
        /// </summary>
        private void DrawHeader()
        {
            EditorGUILayout.BeginVertical("box");
            
            var titleStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 16,
                alignment = TextAnchor.MiddleCenter
            };
            
            EditorGUILayout.LabelField("🏗️ Tilemap3D Editor", titleStyle);
            EditorGUILayout.LabelField("Advanced 3D Tilemap System", EditorStyles.centeredGreyMiniLabel);
            
            EditorGUILayout.EndVertical();
        }
        
        /// <summary>
        /// Draw no tilemap selected message
        /// </summary>
        private void DrawNoTilemapSelected()
        {
            EditorGUILayout.Space(50);
            
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("No Tilemap3D Selected", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("Select a GameObject with Tilemap3D component to start editing.", EditorStyles.wordWrappedLabel);
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("Create New Tilemap3D"))
            {
                CreateNewTilemap();
            }
            
            EditorGUILayout.EndVertical();
        }
        
        /// <summary>
        /// Draw tilemap info
        /// </summary>
        private void DrawTilemapInfo()
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("📋 Tilemap Info", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Selected:", GUILayout.Width(60));
            EditorGUILayout.ObjectField(selectedTilemap, typeof(Tilemap3D), true);
            EditorGUILayout.EndHorizontal();
            
            // Edit mode toggle
            EditorGUILayout.BeginHorizontal();
            var editModeContent = new GUIContent(
                selectedTilemap.IsEditMode ? "🔧 Edit Mode: ON" : "⏸️ Edit Mode: OFF"
            );
            
            GUI.backgroundColor = selectedTilemap.IsEditMode ? Color.green : Color.red;
            if (GUILayout.Button(editModeContent))
            {
                selectedTilemap.SetEditMode(!selectedTilemap.IsEditMode);
                EditorUtility.SetDirty(selectedTilemap);
            }
            GUI.backgroundColor = Color.white;
            
            // Undo/Redo
            if (selectedTilemap.IsEditMode)
            {
                if (GUILayout.Button("↶", GUILayout.Width(30)))
                {
                    selectedTilemap.Undo();
                }
                
                if (GUILayout.Button("↷", GUILayout.Width(30)))
                {
                    selectedTilemap.Redo();
                }
            }
            
            EditorGUILayout.EndHorizontal();
            
            // Statistics
            if (selectedTilemap.IsEditMode)
            {
                EditorGUILayout.Space();
                int totalTiles = 0;
                foreach (var layer in selectedTilemap.Layers)
                {
                    totalTiles += layer.TileCount;
                }
                
                EditorGUILayout.LabelField($"Total Tiles: {totalTiles} | Layers: {selectedTilemap.Layers.Count}");
            }
            
            EditorGUILayout.EndVertical();
        }
        
        /// <summary>
        /// Draw brush panel
        /// </summary>
        private void DrawBrushPanel()
        {
            if (!selectedTilemap.IsEditMode) return;
            
            EditorGUILayout.BeginVertical("box");
            
            showBrushes = EditorGUILayout.Foldout(showBrushes, "🖌️ Brushes", true);
            if (showBrushes)
            {
                // Brush selection grid
                var brushTypes = brushManager.GetAvailableBrushTypes();
                int currentIndex = System.Array.IndexOf(brushTypes, brushManager.ActiveBrushType);
                
                EditorGUILayout.LabelField("Brush Type:");
                
                int columns = 4;
                int rows = Mathf.CeilToInt((float)brushTypes.Length / columns);
                
                for (int row = 0; row < rows; row++)
                {
                    EditorGUILayout.BeginHorizontal();
                    
                    for (int col = 0; col < columns; col++)
                    {
                        int index = row * columns + col;
                        if (index >= brushTypes.Length) break;
                        
                        var brushType = brushTypes[index];
                        var isSelected = index == currentIndex;
                        
                        GUI.backgroundColor = isSelected ? Color.green : Color.white;
                        
                        var content = new GUIContent(
                            brushManager.GetBrushIcon(brushType),
                            brushManager.GetBrushDisplayName(brushType)
                        );
                        
                        if (GUILayout.Button(content, GUILayout.Width(40), GUILayout.Height(40)))
                        {
                            brushManager.SetActiveBrush(brushType);
                            selectedTilemap.SetBrushType(brushType);
                            EditorUtility.SetDirty(selectedTilemap);
                        }
                    }
                    
                    GUI.backgroundColor = Color.white;
                    EditorGUILayout.EndHorizontal();
                }
                
                // Brush size
                EditorGUILayout.Space();
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Size:", GUILayout.Width(40));
                
                int newSize = EditorGUILayout.IntSlider(brushManager.BrushSize, 1, 10);
                if (newSize != brushManager.BrushSize)
                {
                    brushManager.SetBrushSize(newSize);
                    selectedTilemap.SetBrushSize(newSize);
                    EditorUtility.SetDirty(selectedTilemap);
                }
                
                EditorGUILayout.EndHorizontal();
                
                // Active brush info
                EditorGUILayout.LabelField($"Active: {brushManager.GetBrushDisplayName(brushManager.ActiveBrushType)}", EditorStyles.miniLabel);
            }
            
            EditorGUILayout.EndVertical();
        }
        
        /// <summary>
        /// Draw tile library
        /// </summary>
        private void DrawTileLibrary()
        {
            if (!selectedTilemap.IsEditMode) return;
            
            EditorGUILayout.BeginVertical("box");
            
            showTileLibrary = EditorGUILayout.Foldout(showTileLibrary, "🧱 Tile Library", true);
            if (showTileLibrary)
            {
                // Search bar
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("🔍", GUILayout.Width(20));
                searchFilter = EditorGUILayout.TextField(searchFilter);
                if (GUILayout.Button("✖", GUILayout.Width(25)))
                {
                    searchFilter = "";
                    GUI.FocusControl(null);
                }
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.Space();
                
                // Tile grid
                if (selectedTilemap.TileAssets != null && selectedTilemap.TileAssets.Count > 0)
                {
                    tileScrollPosition = EditorGUILayout.BeginScrollView(tileScrollPosition, GUILayout.Height(200));
                    
                    int columns = Mathf.Max(1, Mathf.FloorToInt(position.width / (TILE_SIZE + 10)));
                    int index = 0;
                    
                    foreach (var tileAsset in selectedTilemap.TileAssets)
                    {
                        if (tileAsset == null) continue;
                        
                        // Apply search filter
                        if (!string.IsNullOrEmpty(searchFilter) && 
                            !tileAsset.TileName.ToLower().Contains(searchFilter.ToLower()) &&
                            !tileAsset.Category.ToLower().Contains(searchFilter.ToLower()))
                        {
                            continue;
                        }
                        
                        if (index % columns == 0)
                        {
                            EditorGUILayout.BeginHorizontal();
                        }
                        
                        DrawTileButton(tileAsset);
                        
                        if ((index + 1) % columns == 0 || index == selectedTilemap.TileAssets.Count - 1)
                        {
                            EditorGUILayout.EndHorizontal();
                        }
                        
                        index++;
                    }
                    
                    EditorGUILayout.EndScrollView();
                }
                else
                {
                    EditorGUILayout.HelpBox("No tile assets found. Add TileAsset ScriptableObjects to start building.", MessageType.Info);
                }
                
                // Add tile asset button
                if (GUILayout.Button("➕ Add Tile Assets"))
                {
                    // TODO: Implement asset browser
                    EditorUtility.DisplayDialog("Add Tile Assets", "Asset browser coming soon!", "OK");
                }
            }
            
            EditorGUILayout.EndVertical();
        }
        
        /// <summary>
        /// Draw tile button
        /// </summary>
        private void DrawTileButton(TileAsset tileAsset)
        {
            var isSelected = selectedTilemap.SelectedTileAsset == tileAsset;
            
            // Get preview
            Texture2D preview = null;
            if (tileAsset.Icon != null)
            {
                preview = tileAsset.Icon.texture;
            }
            else if (tileAsset.Prefab != null)
            {
                preview = AssetPreview.GetAssetPreview(tileAsset.Prefab);
            }
            
            // Button style
            var style = new GUIStyle(GUI.skin.button);
            if (isSelected)
            {
                GUI.backgroundColor = Color.green;
            }
            
            var content = new GUIContent(preview, $"{tileAsset.TileName}\nCategory: {tileAsset.Category}");
            
            if (GUILayout.Button(content, style, GUILayout.Width(TILE_SIZE), GUILayout.Height(TILE_SIZE)))
            {
                selectedTilemap.SetSelectedTileAsset(tileAsset);
                brushManager.SetSelectedTileAsset(tileAsset);
                EditorUtility.SetDirty(selectedTilemap);
            }
            
            GUI.backgroundColor = Color.white;
            
            // Tile name
            var rect = GUILayoutUtility.GetLastRect();
            var nameRect = new Rect(rect.x, rect.y + TILE_SIZE - 20, rect.width, 20);
            
            var nameStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                alignment = TextAnchor.MiddleCenter,
                fontSize = 8
            };
            
            GUI.Label(nameRect, tileAsset.TileName, nameStyle);
        }
