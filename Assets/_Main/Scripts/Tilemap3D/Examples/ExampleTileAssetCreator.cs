using UnityEngine;
using UnityEditor;
using Tilemap3D.Core;

namespace Tilemap3D.Examples
{
    /// <summary>
    /// Utility to create example tile assets for testing
    /// </summary>
    public class ExampleTileAssetCreator
    {
        [MenuItem("Tilemap3D/Create Example Tile Assets")]
        public static void CreateExampleTileAssets()
        {
            // Create folder if it doesn't exist
            string folderPath = "Assets/_Main/TileAssets";
            if (!AssetDatabase.IsValidFolder(folderPath))
            {
                AssetDatabase.CreateFolder("Assets/_Main", "TileAssets");
            }
            
            // Create basic tile assets
            CreateBasicTileAsset("Grass Tile", "Environment", Color.green);
            CreateBasicTileAsset("Stone Tile", "Environment", Color.gray);
            CreateBasicTileAsset("Water Tile", "Environment", Color.blue);
            CreateBasicTileAsset("Sand Tile", "Environment", Color.yellow);
            CreateBasicTileAsset("Wood Tile", "Building", new Color(0.6f, 0.3f, 0.1f));
            CreateBasicTileAsset("Metal Tile", "Building", Color.white);
            
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log("✅ Created example tile assets in " + folderPath);
        }
        
        private static void CreateBasicTileAsset(string tileName, string category, Color color)
        {
            // Create the tile asset
            var tileAsset = ScriptableObject.CreateInstance<TileAsset>();
            
            // Set basic properties using reflection since fields are private
            var tileNameField = typeof(TileAsset).GetField("tileName", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var categoryField = typeof(TileAsset).GetField("category", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var tintColorField = typeof(TileAsset).GetField("tintColor", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var isWalkableField = typeof(TileAsset).GetField("isWalkable", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var hasColliderField = typeof(TileAsset).GetField("hasCollider", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            tileNameField?.SetValue(tileAsset, tileName);
            categoryField?.SetValue(tileAsset, category);
            tintColorField?.SetValue(tileAsset, color);
            isWalkableField?.SetValue(tileAsset, true);
            hasColliderField?.SetValue(tileAsset, true);
            
            // Try to find a cube prefab to use
            var cubePrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/_Main/Prefabs/tile0.prefab");
            if (cubePrefab == null)
            {
                // Create a simple cube prefab if none exists
                cubePrefab = CreateSimpleCubePrefab(tileName, color);
            }
            
            var prefabField = typeof(TileAsset).GetField("prefab", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            prefabField?.SetValue(tileAsset, cubePrefab);
            
            // Save the asset
            string assetPath = $"Assets/_Main/TileAssets/{tileName.Replace(" ", "")}.asset";
            AssetDatabase.CreateAsset(tileAsset, assetPath);
        }
        
        private static GameObject CreateSimpleCubePrefab(string name, Color color)
        {
            // Create a simple cube
            var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = name.Replace(" ", "") + "Prefab";
            
            // Create material
            var material = new Material(Shader.Find("Standard"));
            material.color = color;
            material.name = name.Replace(" ", "") + "Material";
            
            // Apply material
            var renderer = cube.GetComponent<Renderer>();
            renderer.material = material;
            
            // Save material
            string materialPath = $"Assets/_Main/TileAssets/{material.name}.mat";
            AssetDatabase.CreateAsset(material, materialPath);
            
            // Save prefab
            string prefabPath = $"Assets/_Main/TileAssets/{cube.name}.prefab";
            var prefab = PrefabUtility.SaveAsPrefabAsset(cube, prefabPath);
            
            // Clean up scene object
            Object.DestroyImmediate(cube);
            
            return prefab;
        }
        
        [MenuItem("Tilemap3D/Setup Example Scene")]
        public static void SetupExampleScene()
        {
            // Create a tilemap in the scene
            var tilemapGO = new GameObject("Example Tilemap3D");
            var tilemap = tilemapGO.AddComponent<Tilemap3D>();
            
            // Load example tile assets
            string[] guids = AssetDatabase.FindAssets("t:TileAsset", new[] { "Assets/_Main/TileAssets" });
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var tileAsset = AssetDatabase.LoadAssetAtPath<TileAsset>(path);
                
                if (tileAsset != null)
                {
                    tilemap.TileAssets.Add(tileAsset);
                }
            }
            
            // Set first tile as selected
            if (tilemap.TileAssets.Count > 0)
            {
                tilemap.SetSelectedTileAsset(tilemap.TileAssets[0]);
            }
            
            // Enable edit mode
            tilemap.SetEditMode(true);
            
            // Select the tilemap
            Selection.activeGameObject = tilemapGO;
            
            Debug.Log("✅ Created example tilemap scene setup!");
        }
        
        [MenuItem("Tilemap3D/Create Documentation")]
        public static void CreateDocumentation()
        {
            string documentation = @"# Tilemap3D System Documentation

## Overview
The Tilemap3D system is an advanced 3D tilemap editor for Unity with the following features:

### Core Features
- **Multi-layer support** with visibility, lock, and opacity controls
- **Advanced brush system** with multiple brush types (Single, Rectangle, Circle, Line, Flood Fill, Eyedropper, Eraser)
- **Undo/Redo system** for all editing operations
- **TileAsset ScriptableObjects** for flexible tile definitions
- **Performance optimization** with chunking and culling
- **Modern UI** with intuitive editor interface

### Getting Started

1. **Create Tile Assets**
   - Use `Tilemap3D > Create Example Tile Assets` to generate sample tiles
   - Or create your own using `Create > Tilemap3D > Tile Asset`

2. **Setup Scene**
   - Use `Tilemap3D > Setup Example Scene` for quick start
   - Or manually add Tilemap3D component to a GameObject

3. **Start Editing**
   - Enable Edit Mode in the inspector
   - Select tiles from the tile palette
   - Choose brush type and start painting!

### Brush Types
- **Single (1)**: Paint individual tiles
- **Rectangle (2)**: Paint rectangular areas
- **Circle (3)**: Paint circular areas
- **Line**: Paint lines between two points
- **Flood Fill**: Fill connected areas
- **Eyedropper (I)**: Pick tiles from the scene
- **Eraser (E)**: Remove tiles

### Keyboard Shortcuts
- **Ctrl+Z**: Undo
- **Ctrl+Shift+Z**: Redo
- **1-3**: Switch brush types
- **I**: Eyedropper tool
- **E**: Eraser tool

### Layer System
- Create multiple layers for organization
- Control visibility and lock state
- Adjust opacity and blend modes
- Reorder layers by dragging

### Performance Features
- **Chunking**: Automatically groups tiles for better performance
- **Culling**: Hides distant chunks to improve framerate
- **LOD System**: (Coming soon) Level-of-detail for distant tiles

### Advanced Features
- **Auto-tiling**: Automatic tile connections based on rules
- **Animation**: Support for animated tiles
- **Physics**: Configurable colliders and physics materials
- **Audio**: Placement and destruction sound effects

## API Reference

### Tilemap3D Class
Main component for 3D tilemaps.

#### Key Methods
- `PlaceTile(Vector3Int position, TileAsset tileAsset, int layerIndex)`
- `RemoveTile(Vector3Int position, int layerIndex)`
- `CreateLayer(string layerName)`
- `SetActiveLayer(int layerIndex)`
- `Undo()` / `Redo()`

### TileAsset Class
ScriptableObject defining tile properties.

#### Key Properties
- `TileName`: Display name
- `Prefab`: GameObject to instantiate
- `Category`: Organization category
- `TileType`: Behavior type (Static, Animated, Interactive, etc.)
- `IsWalkable`: Navigation property
- `HasCollider`: Physics property

### BrushManager Class
Manages different brush types and painting operations.

#### Key Methods
- `SetActiveBrush(BrushType brushType)`
- `Paint(Tilemap3D tilemap, Vector3Int position)`
- `Erase(Tilemap3D tilemap, Vector3Int position)`

## Tips and Best Practices

1. **Organization**
   - Use categories to organize tile assets
   - Create separate layers for different elements (ground, walls, decorations)
   - Use descriptive names for layers and tiles

2. **Performance**
   - Enable chunking for large tilemaps
   - Use appropriate chunk sizes (16x16x16 is usually good)
   - Consider LOD for very large worlds

3. **Workflow**
   - Start with basic tiles and expand gradually
   - Use the eyedropper tool to quickly select existing tiles
   - Save templates for reusable patterns

4. **Customization**
   - Create custom TileAsset types for special behaviors
   - Extend the brush system for specialized tools
   - Use events to integrate with other systems

## Troubleshooting

**Q: Tiles are not appearing**
A: Check that the layer is visible and not locked. Ensure the tile asset has a valid prefab.

**Q: Performance is slow**
A: Enable chunking and adjust chunk size. Check if culling is enabled.

**Q: Undo/Redo not working**
A: Make sure you're using the tilemap's built-in painting methods, not directly manipulating GameObjects.

**Q: Brushes not working**
A: Ensure edit mode is enabled and a tile asset is selected.

## Future Roadmap

- Import/Export functionality
- Advanced auto-tiling rules
- Procedural generation tools
- Runtime editing support
- Multiplayer synchronization
- VR/AR support

For more information, visit: [Tilemap3D Documentation](https://github.com/your-repo/tilemap3d)
";

            string docPath = "Assets/_Main/Tilemap3D_Documentation.md";
            System.IO.File.WriteAllText(docPath, documentation);
            AssetDatabase.Refresh();
            
            Debug.Log("✅ Created documentation at " + docPath);
        }
    }
}
