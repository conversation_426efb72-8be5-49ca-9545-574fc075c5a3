using UnityEngine;
using Tilemap3D.Core;
using Tilemap3D.Tools;

namespace Tilemap3D.Examples
{
    /// <summary>
    /// Test script to verify Tilemap3D system functionality
    /// </summary>
    public class Tilemap3DTest : MonoBehaviour
    {
        [Header("Test Settings")]
        public Tilemap3D tilemap;
        public TileAsset testTileAsset;
        public bool runTests = false;
        
        private BrushManager brushManager;
        
        private void Start()
        {
            if (runTests)
            {
                RunTests();
            }
        }
        
        private void RunTests()
        {
            Debug.Log("🧪 Starting Tilemap3D Tests...");
            
            // Test 1: Basic tilemap functionality
            TestBasicTilemapFunctionality();
            
            // Test 2: Layer system
            TestLayerSystem();
            
            // Test 3: Brush system
            TestBrushSystem();
            
            // Test 4: Undo/Redo system
            TestUndoRedoSystem();
            
            Debug.Log("✅ All Tilemap3D tests completed!");
        }
        
        private void TestBasicTilemapFunctionality()
        {
            Debug.Log("🔧 Testing basic tilemap functionality...");
            
            if (tilemap == null)
            {
                Debug.LogError("❌ Tilemap is null!");
                return;
            }
            
            if (testTileAsset == null)
            {
                Debug.LogError("❌ Test tile asset is null!");
                return;
            }
            
            // Test placing a tile
            Vector3Int testPosition = new Vector3Int(0, 0, 0);
            bool placed = tilemap.PlaceTile(testPosition, testTileAsset);
            
            if (placed)
            {
                Debug.Log("✅ Tile placed successfully");
                
                // Test getting the tile
                var placedTile = tilemap.GetTile(testPosition);
                if (placedTile != null && placedTile.TileAsset == testTileAsset)
                {
                    Debug.Log("✅ Tile retrieved successfully");
                    
                    // Test removing the tile
                    bool removed = tilemap.RemoveTile(testPosition);
                    if (removed)
                    {
                        Debug.Log("✅ Tile removed successfully");
                    }
                    else
                    {
                        Debug.LogError("❌ Failed to remove tile");
                    }
                }
                else
                {
                    Debug.LogError("❌ Failed to retrieve tile or tile asset mismatch");
                }
            }
            else
            {
                Debug.LogError("❌ Failed to place tile");
            }
        }
        
        private void TestLayerSystem()
        {
            Debug.Log("🔧 Testing layer system...");
            
            // Test creating a new layer
            var newLayer = tilemap.CreateLayer("Test Layer");
            if (newLayer != null)
            {
                Debug.Log("✅ Layer created successfully");
                
                // Test layer properties
                newLayer.IsVisible = false;
                newLayer.IsLocked = true;
                newLayer.Opacity = 0.5f;
                
                Debug.Log($"✅ Layer properties set: Visible={newLayer.IsVisible}, Locked={newLayer.IsLocked}, Opacity={newLayer.Opacity}");
                
                // Test placing tile on specific layer
                int layerIndex = tilemap.Layers.Count - 1;
                tilemap.SetActiveLayer(layerIndex);
                
                Vector3Int testPosition = new Vector3Int(1, 0, 1);
                bool placed = tilemap.PlaceTile(testPosition, testTileAsset, layerIndex);
                
                if (placed)
                {
                    Debug.Log("✅ Tile placed on specific layer");
                }
                else
                {
                    Debug.Log("⚠️ Tile not placed (might be due to locked layer)");
                }
                
                // Test removing layer
                int layerCount = tilemap.Layers.Count;
                bool removed = tilemap.RemoveLayer(layerIndex);
                if (removed && tilemap.Layers.Count == layerCount - 1)
                {
                    Debug.Log("✅ Layer removed successfully");
                }
                else
                {
                    Debug.LogError("❌ Failed to remove layer");
                }
            }
            else
            {
                Debug.LogError("❌ Failed to create layer");
            }
        }
        
        private void TestBrushSystem()
        {
            Debug.Log("🔧 Testing brush system...");
            
            // Initialize brush manager
            brushManager = new BrushManager();
            brushManager.Initialize();
            brushManager.SetSelectedTileAsset(testTileAsset);
            
            // Test different brush types
            var brushTypes = brushManager.GetAvailableBrushTypes();
            
            foreach (var brushType in brushTypes)
            {
                brushManager.SetActiveBrush(brushType);
                var brush = brushManager.ActiveBrush;
                
                if (brush != null)
                {
                    Debug.Log($"✅ {brushType} brush created successfully");
                    
                    // Test brush properties
                    brush.SetBrushSize(3);
                    brush.SetSelectedTile(testTileAsset);
                    
                    // Test preview positions
                    var previewPositions = brush.GetPreviewPositions(Vector3Int.zero);
                    Debug.Log($"✅ {brushType} brush preview positions: {previewPositions.Count}");
                }
                else
                {
                    Debug.LogError($"❌ Failed to create {brushType} brush");
                }
            }
            
            // Test brush painting
            brushManager.SetActiveBrush(BrushType.Single);
            Vector3Int paintPosition = new Vector3Int(2, 0, 2);
            
            if (brushManager.CanPaint(tilemap, paintPosition))
            {
                brushManager.Paint(tilemap, paintPosition);
                
                var paintedTile = tilemap.GetTile(paintPosition);
                if (paintedTile != null)
                {
                    Debug.Log("✅ Brush painting successful");
                    
                    // Test brush erasing
                    brushManager.Erase(tilemap, paintPosition);
                    var erasedTile = tilemap.GetTile(paintPosition);
                    if (erasedTile == null)
                    {
                        Debug.Log("✅ Brush erasing successful");
                    }
                    else
                    {
                        Debug.LogError("❌ Brush erasing failed");
                    }
                }
                else
                {
                    Debug.LogError("❌ Brush painting failed");
                }
            }
            else
            {
                Debug.LogError("❌ Cannot paint at position");
            }
        }
        
        private void TestUndoRedoSystem()
        {
            Debug.Log("🔧 Testing undo/redo system...");
            
            if (tilemap.History == null)
            {
                Debug.LogError("❌ History system is null");
                return;
            }
            
            // Place a tile
            Vector3Int testPosition = new Vector3Int(3, 0, 3);
            tilemap.PlaceTile(testPosition, testTileAsset);
            
            var placedTile = tilemap.GetTile(testPosition);
            if (placedTile != null)
            {
                Debug.Log("✅ Tile placed for undo test");
                
                // Test undo
                if (tilemap.History.CanUndo)
                {
                    tilemap.Undo();
                    var undoTile = tilemap.GetTile(testPosition);
                    if (undoTile == null)
                    {
                        Debug.Log("✅ Undo successful");
                        
                        // Test redo
                        if (tilemap.History.CanRedo)
                        {
                            tilemap.Redo();
                            var redoTile = tilemap.GetTile(testPosition);
                            if (redoTile != null)
                            {
                                Debug.Log("✅ Redo successful");
                            }
                            else
                            {
                                Debug.LogError("❌ Redo failed");
                            }
                        }
                        else
                        {
                            Debug.LogError("❌ Cannot redo");
                        }
                    }
                    else
                    {
                        Debug.LogError("❌ Undo failed");
                    }
                }
                else
                {
                    Debug.LogError("❌ Cannot undo");
                }
            }
            else
            {
                Debug.LogError("❌ Failed to place tile for undo test");
            }
        }
        
        /// <summary>
        /// Manual test method that can be called from inspector
        /// </summary>
        [ContextMenu("Run Manual Test")]
        public void RunManualTest()
        {
            if (tilemap == null)
            {
                tilemap = GetComponent<Tilemap3D>();
            }
            
            RunTests();
        }
        
        /// <summary>
        /// Create test setup
        /// </summary>
        [ContextMenu("Create Test Setup")]
        public void CreateTestSetup()
        {
            if (tilemap == null)
            {
                tilemap = GetComponent<Tilemap3D>();
                if (tilemap == null)
                {
                    tilemap = gameObject.AddComponent<Tilemap3D>();
                }
            }
            
            tilemap.SetEditMode(true);
            
            Debug.Log("✅ Test setup created. Assign a TileAsset to testTileAsset field and run tests.");
        }
    }
}
