using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
public class Tilemap3D : MonoBehaviour
{
    //object
    public struct Mode
    {
        public string name;
        public bool isActive;

        public Mode(string name, bool isActive)
        {
            this.name = name;
            this.isActive = isActive;
        }
    }
    //fields
    [SerializeField] private GameObject[] _tilePrefabs;
    [SerializeField] private float _height = 0;
    private int _selectedTileIndex = 0;
    private readonly Vector3 PIVOT = new Vector3(0.5f, 0f, 0.5f);
    //status
    private bool _isEditMode = false;
    private Mode[] _modes = { new Mode("Hand", true), new Mode("Put", false), new Mode("Remove", false) };//hand, put, remove

    //properties
    public GameObject[] TilePrefabs { get { return _tilePrefabs; } set { _tilePrefabs = value; } }
    public int SelectedTileIndex { get { return _selectedTileIndex; } set { _selectedTileIndex = value; } }
    public bool IsEditMode { get { return _isEditMode; } set { _isEditMode = value; } }
    public Mode[] Modes { get { return _modes; } }

    //cached
    private Dictionary<Vector3Int, GameObject> _tiles = new();

    public void PlaceTile(Vector3 worldPosition)
    {
        Vector3Int position = Vector3Int.CeilToInt(worldPosition);
        if (_tiles.ContainsKey(position)) return;

        GameObject prefab = _tilePrefabs[_selectedTileIndex];
        Vector3 putPosition = position;
        putPosition.y = _height;
        putPosition -= PIVOT;
        GameObject tile = Instantiate(prefab, putPosition, Quaternion.identity, transform);
        _tiles[position] = tile;
    }

    public void RemoveTile(Vector3 worldPosition)
    {
        Vector3Int gridPos = Vector3Int.CeilToInt(worldPosition);
        if (_tiles.TryGetValue(gridPos, out GameObject tile))
        {
            _tiles.Remove(gridPos);
            DestroyImmediate(tile);
        }
    }

    public void SelectMode(int index)
    {
        if (index < 0 || index >= _modes.Length) return;
        for (int i = 0; i < _modes.Length; i++)
        {
            _modes[i].isActive = false;
        }
        _modes[index].isActive = true;
    }

    public void PlaceTileAtWorldPosition(Vector3 worldPosition)
    {
        Vector3Int pos = Vector3Int.CeilToInt(worldPosition);
        PlaceTile(pos);
    }

    public void RemoveTileAtWorldPosition(Vector3 worldPosition)
    {
        Vector3Int pos = Vector3Int.CeilToInt(worldPosition); // Trừ PIVOT
        RemoveTile(pos);
    }
}

