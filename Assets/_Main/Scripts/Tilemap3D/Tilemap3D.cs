using System.Collections.Generic;
using UnityEngine;
using Tilemap3D.Core;
using Tilemap3D.Tools;

namespace Tilemap3D
{
    /// <summary>
    /// Advanced 3D Tilemap system with layers, brushes, and optimization
    /// </summary>
    [ExecuteInEditMode]
    public class Tilemap3D : MonoBehaviour
    {
        [Header("Tilemap Settings")]
        [SerializeField] private Vector3Int gridSize = new Vector3Int(100, 10, 100);
        [SerializeField] private Vector3 cellSize = Vector3.one;
        [SerializeField] private Vector3 cellGap = Vector3.zero;
        [SerializeField] private bool showGrid = true;
        [SerializeField] private Color gridColor = Color.white;

        [Header("Layers")]
        [SerializeField] private List<TilemapLayer> layers = new List<TilemapLayer>();
        [SerializeField] private int activeLayerIndex = 0;

        [Header("Tile Assets")]
        [SerializeField] private List<TileAsset> tileAssets = new List<TileAsset>();
        [SerializeField] private TileAsset selectedTileAsset;

        [Header("Editor Settings")]
        [SerializeField] private bool isEditMode = false;
        [SerializeField] private BrushType activeBrushType = BrushType.Single;
        [SerializeField] private int brushSize = 1;

        [Header("Performance")]
        [SerializeField] private bool enableChunking = true;
        [SerializeField] private Vector3Int chunkSize = new Vector3Int(16, 16, 16);
        [SerializeField] private bool enableCulling = true;
        [SerializeField] private float cullingDistance = 100f;

        // Runtime data
        private Dictionary<Vector3Int, TileChunk> chunks = new Dictionary<Vector3Int, TileChunk>();
        private TilemapHistory history = new TilemapHistory();
        private bool isDirty = false;

        // Events
        public System.Action<Vector3Int, TileAsset, int> OnTilePlaced;
        public System.Action<Vector3Int, int> OnTileRemoved;
        public System.Action<int> OnLayerChanged;
        public System.Action OnTilemapChanged;

        // Properties
        public Vector3Int GridSize => gridSize;
        public Vector3 CellSize => cellSize;
        public Vector3 CellGap => cellGap;
        public bool ShowGrid => showGrid;
        public Color GridColor => gridColor;
        public List<TilemapLayer> Layers => layers;
        public int ActiveLayerIndex => activeLayerIndex;
        public TilemapLayer ActiveLayer => activeLayerIndex >= 0 && activeLayerIndex < layers.Count ? layers[activeLayerIndex] : null;
        public List<TileAsset> TileAssets => tileAssets;
        public TileAsset SelectedTileAsset => selectedTileAsset;
        public bool IsEditMode => isEditMode;
        public BrushType ActiveBrushType => activeBrushType;
        public int BrushSize => brushSize;
        public bool EnableChunking => enableChunking;
        public Vector3Int ChunkSize => chunkSize;
        public TilemapHistory History => history;
        public bool IsDirty => isDirty;

        /// <summary>
        /// Initialize tilemap
        /// </summary>
        private void Awake()
        {
            InitializeTilemap();
        }

        /// <summary>
        /// Initialize tilemap system
        /// </summary>
        private void InitializeTilemap()
        {
            // Create default layer if none exists
            if (layers.Count == 0)
            {
                CreateLayer("Default Layer");
            }

            // Initialize layers
            for (int i = 0; i < layers.Count; i++)
            {
                layers[i].Initialize(transform, i);
                layers[i].OnLayerChanged += OnLayerChangedInternal;
                layers[i].OnTileAdded += OnTileAddedInternal;
                layers[i].OnTileRemoved += OnTileRemovedInternal;
            }

            // Initialize history
            history.Initialize(this);
        }

        /// <summary>
        /// Update tilemap
        /// </summary>
        private void Update()
        {
            if (enableCulling)
            {
                UpdateChunkCulling();
            }
        }

        /// <summary>
        /// Create new layer
        /// </summary>
        public TilemapLayer CreateLayer(string layerName = "New Layer")
        {
            var layer = new TilemapLayer();
            layer.LayerName = layerName;
            layer.Initialize(transform, layers.Count);
            layer.OnLayerChanged += OnLayerChangedInternal;
            layer.OnTileAdded += OnTileAddedInternal;
            layer.OnTileRemoved += OnTileRemovedInternal;

            layers.Add(layer);
            MarkDirty();

            return layer;
        }

        /// <summary>
        /// Remove layer
        /// </summary>
        public bool RemoveLayer(int layerIndex)
        {
            if (layerIndex < 0 || layerIndex >= layers.Count || layers.Count <= 1) return false;

            var layer = layers[layerIndex];
            layer.ClearAllTiles();

            if (layer.LayerTransform != null)
            {
                DestroyImmediate(layer.LayerTransform.gameObject);
            }

            layers.RemoveAt(layerIndex);

            // Adjust active layer index
            if (activeLayerIndex >= layerIndex && activeLayerIndex > 0)
            {
                activeLayerIndex--;
            }

            MarkDirty();
            return true;
        }

        /// <summary>
        /// Set active layer
        /// </summary>
        public void SetActiveLayer(int layerIndex)
        {
            if (layerIndex >= 0 && layerIndex < layers.Count)
            {
                activeLayerIndex = layerIndex;
                OnLayerChanged?.Invoke(layerIndex);
            }
        }

        /// <summary>
        /// Place tile at position
        /// </summary>
        public bool PlaceTile(Vector3Int position, TileAsset tileAsset = null, int layerIndex = -1)
        {
            if (tileAsset == null) tileAsset = selectedTileAsset;
            if (tileAsset == null) return false;

            if (layerIndex == -1) layerIndex = activeLayerIndex;
            if (layerIndex < 0 || layerIndex >= layers.Count) return false;

            var layer = layers[layerIndex];
            if (layer.IsLocked) return false;

            // Record for undo
            var undoData = new TileChangeData
            {
                position = position,
                layerIndex = layerIndex,
                oldTileAsset = layer.GetTile(position)?.TileAsset,
                newTileAsset = tileAsset,
                changeType = TileChangeType.Place
            };

            bool success = layer.AddTile(position, tileAsset);
            if (success)
            {
                history.RecordChange(undoData);
                OnTilePlaced?.Invoke(position, tileAsset, layerIndex);
                MarkDirty();

                // Update chunk
                if (enableChunking)
                {
                    UpdateChunk(GetChunkPosition(position));
                }
            }

            return success;
        }

        /// <summary>
        /// Remove tile at position
        /// </summary>
        public bool RemoveTile(Vector3Int position, int layerIndex = -1)
        {
            if (layerIndex == -1) layerIndex = activeLayerIndex;
            if (layerIndex < 0 || layerIndex >= layers.Count) return false;

            var layer = layers[layerIndex];
            if (layer.IsLocked) return false;

            var existingTile = layer.GetTile(position);
            if (existingTile == null) return false;

            // Record for undo
            var undoData = new TileChangeData
            {
                position = position,
                layerIndex = layerIndex,
                oldTileAsset = existingTile.TileAsset,
                newTileAsset = null,
                changeType = TileChangeType.Remove
            };

            bool success = layer.RemoveTile(position);
            if (success)
            {
                history.RecordChange(undoData);
                OnTileRemoved?.Invoke(position, layerIndex);
                MarkDirty();

                // Update chunk
                if (enableChunking)
                {
                    UpdateChunk(GetChunkPosition(position));
                }
            }

            return success;
        }

        /// <summary>
        /// Get tile at position
        /// </summary>
        public TileInstance GetTile(Vector3Int position, int layerIndex = -1)
        {
            if (layerIndex == -1) layerIndex = activeLayerIndex;
            if (layerIndex < 0 || layerIndex >= layers.Count) return null;

            return layers[layerIndex].GetTile(position);
        }

        /// <summary>
        /// Check if position has tile
        /// </summary>
        public bool HasTile(Vector3Int position, int layerIndex = -1)
        {
            if (layerIndex == -1) layerIndex = activeLayerIndex;
            if (layerIndex < 0 || layerIndex >= layers.Count) return false;

            return layers[layerIndex].HasTile(position);
        }

        /// <summary>
        /// Convert world position to grid position
        /// </summary>
        public Vector3Int WorldToGrid(Vector3 worldPosition)
        {
            Vector3 localPosition = transform.InverseTransformPoint(worldPosition);
            Vector3 gridPosition = new Vector3(
                localPosition.x / (cellSize.x + cellGap.x),
                localPosition.y / (cellSize.y + cellGap.y),
                localPosition.z / (cellSize.z + cellGap.z)
            );

            return Vector3Int.FloorToInt(gridPosition);
        }

        /// <summary>
        /// Convert grid position to world position
        /// </summary>
        public Vector3 GridToWorld(Vector3Int gridPosition)
        {
            Vector3 localPosition = new Vector3(
                gridPosition.x * (cellSize.x + cellGap.x),
                gridPosition.y * (cellSize.y + cellGap.y),
                gridPosition.z * (cellSize.z + cellGap.z)
            );

            return transform.TransformPoint(localPosition);
        }

        /// <summary>
        /// Get chunk position for a grid position
        /// </summary>
        private Vector3Int GetChunkPosition(Vector3Int gridPosition)
        {
            return new Vector3Int(
                Mathf.FloorToInt((float)gridPosition.x / chunkSize.x),
                Mathf.FloorToInt((float)gridPosition.y / chunkSize.y),
                Mathf.FloorToInt((float)gridPosition.z / chunkSize.z)
            );
        }

        /// <summary>
        /// Update chunk
        /// </summary>
        private void UpdateChunk(Vector3Int chunkPosition)
        {
            if (!chunks.TryGetValue(chunkPosition, out TileChunk chunk))
            {
                chunk = new TileChunk(chunkPosition, chunkSize);
                chunks[chunkPosition] = chunk;
            }

            chunk.UpdateChunk(this);
        }

        /// <summary>
        /// Update chunk culling
        /// </summary>
        private void UpdateChunkCulling()
        {
            if (Camera.main == null) return;

            Vector3 cameraPosition = Camera.main.transform.position;

            foreach (var chunk in chunks.Values)
            {
                float distance = Vector3.Distance(cameraPosition, chunk.WorldCenter);
                chunk.SetVisible(distance <= cullingDistance);
            }
        }

        /// <summary>
        /// Clear all tiles in all layers
        /// </summary>
        public void ClearAllTiles()
        {
            foreach (var layer in layers)
            {
                layer.ClearAllTiles();
            }

            chunks.Clear();
            MarkDirty();
        }

        /// <summary>
        /// Get tilemap bounds
        /// </summary>
        public Bounds GetBounds()
        {
            if (layers.Count == 0) return new Bounds();

            Bounds bounds = new Bounds();
            bool first = true;

            foreach (var layer in layers)
            {
                var layerBounds = layer.GetBounds();
                if (layerBounds.size != Vector3.zero)
                {
                    if (first)
                    {
                        bounds = layerBounds;
                        first = false;
                    }
                    else
                    {
                        bounds.Encapsulate(layerBounds);
                    }
                }
            }

            return bounds;
        }

        /// <summary>
        /// Set selected tile asset
        /// </summary>
        public void SetSelectedTileAsset(TileAsset tileAsset)
        {
            selectedTileAsset = tileAsset;
        }

        /// <summary>
        /// Set brush type
        /// </summary>
        public void SetBrushType(BrushType brushType)
        {
            activeBrushType = brushType;
        }

        /// <summary>
        /// Set brush size
        /// </summary>
        public void SetBrushSize(int size)
        {
            brushSize = Mathf.Max(1, size);
        }

        /// <summary>
        /// Set edit mode
        /// </summary>
        public void SetEditMode(bool editMode)
        {
            isEditMode = editMode;
        }

        /// <summary>
        /// Undo last action
        /// </summary>
        public void Undo()
        {
            history.Undo();
        }

        /// <summary>
        /// Redo last undone action
        /// </summary>
        public void Redo()
        {
            history.Redo();
        }

        /// <summary>
        /// Mark tilemap as dirty
        /// </summary>
        private void MarkDirty()
        {
            isDirty = true;
            OnTilemapChanged?.Invoke();
        }

        /// <summary>
        /// Clear dirty flag
        /// </summary>
        public void ClearDirtyFlag()
        {
            isDirty = false;
            foreach (var layer in layers)
            {
                layer.ClearDirtyFlag();
            }
        }

        /// <summary>
        /// Internal layer changed handler
        /// </summary>
        private void OnLayerChangedInternal(TilemapLayer layer)
        {
            MarkDirty();
        }

        /// <summary>
        /// Internal tile added handler
        /// </summary>
        private void OnTileAddedInternal(Vector3Int position, TileInstance tile)
        {
            // Handle auto-tiling if enabled
            if (tile.TileAsset.SupportsAutoTiling)
            {
                UpdateAutoTiling(position, tile.LayerIndex);
            }
        }

        /// <summary>
        /// Internal tile removed handler
        /// </summary>
        private void OnTileRemovedInternal(Vector3Int position, TileInstance tile)
        {
            // Update neighboring auto-tiles
            UpdateNeighborAutoTiling(position, tile.LayerIndex);
        }

        /// <summary>
        /// Update auto-tiling for a position
        /// </summary>
        private void UpdateAutoTiling(Vector3Int position, int layerIndex)
        {
            // Implementation for auto-tiling logic
            // This would check neighboring tiles and apply appropriate rules
        }

        /// <summary>
        /// Update auto-tiling for neighbors
        /// </summary>
        private void UpdateNeighborAutoTiling(Vector3Int position, int layerIndex)
        {
            // Check all 8 neighboring positions and update their auto-tiling
            for (int x = -1; x <= 1; x++)
            {
                for (int z = -1; z <= 1; z++)
                {
                    if (x == 0 && z == 0) continue;

                    Vector3Int neighborPos = position + new Vector3Int(x, 0, z);
                    var neighborTile = GetTile(neighborPos, layerIndex);

                    if (neighborTile != null && neighborTile.TileAsset.SupportsAutoTiling)
                    {
                        UpdateAutoTiling(neighborPos, layerIndex);
                    }
                }
            }
        }

        /// <summary>
        /// Validate tilemap
        /// </summary>
        private void OnValidate()
        {
            // Clamp values
            gridSize = new Vector3Int(
                Mathf.Max(1, gridSize.x),
                Mathf.Max(1, gridSize.y),
                Mathf.Max(1, gridSize.z)
            );

            cellSize = new Vector3(
                Mathf.Max(0.1f, cellSize.x),
                Mathf.Max(0.1f, cellSize.y),
                Mathf.Max(0.1f, cellSize.z)
            );

            chunkSize = new Vector3Int(
                Mathf.Max(1, chunkSize.x),
                Mathf.Max(1, chunkSize.y),
                Mathf.Max(1, chunkSize.z)
            );

            brushSize = Mathf.Max(1, brushSize);
            cullingDistance = Mathf.Max(1f, cullingDistance);

            // Clamp active layer index
            if (layers.Count > 0)
            {
                activeLayerIndex = Mathf.Clamp(activeLayerIndex, 0, layers.Count - 1);
            }
        }

        /// <summary>
        /// Draw gizmos
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (showGrid)
            {
                DrawGrid();
            }

            DrawBounds();
        }

        /// <summary>
        /// Draw grid gizmos
        /// </summary>
        private void DrawGrid()
        {
            Gizmos.color = gridColor;

            Vector3 center = transform.position;
            Vector3 size = new Vector3(
                gridSize.x * (cellSize.x + cellGap.x),
                gridSize.y * (cellSize.y + cellGap.y),
                gridSize.z * (cellSize.z + cellGap.z)
            );

            Gizmos.DrawWireCube(center, size);
        }

        /// <summary>
        /// Draw bounds gizmos
        /// </summary>
        private void DrawBounds()
        {
            var bounds = GetBounds();
            if (bounds.size != Vector3.zero)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(bounds.center, bounds.size);
            }
        }
    }
}
