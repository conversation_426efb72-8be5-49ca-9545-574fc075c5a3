using UnityEditor;
using UnityEngine;
using Tilemap3D.Core;
using Tilemap3D.Tools;

namespace Tilemap3D.Editor
{
    /// <summary>
    /// Advanced custom editor for Tilemap3D with modern UI
    /// </summary>
    [CustomEditor(typeof(Tilemap3D))]
    public class Tilemap3DEditor : UnityEditor.Editor
    {
        private Tilemap3D tilemap;
        private BrushManager brushManager;
        private Vector3Int lastHoverPosition = Vector3Int.zero;
        private bool isMouseDown = false;

        // UI State
        private bool showTileAssets = true;
        private bool showLayers = true;
        private bool showBrushes = true;
        private bool showSettings = false;
        private Vector2 tileScrollPosition;
        private Vector2 layerScrollPosition;

        // Constants
        private const int TILE_PREVIEW_SIZE = 64;
        private const int LAYER_HEIGHT = 25;

        private void OnEnable()
        {
            tilemap = (Tilemap3D)target;

            // Initialize brush manager
            if (brushManager == null)
            {
                brushManager = new BrushManager();
                brushManager.Initialize();
                brushManager.OnTileSelected += OnTileSelected;
            }

            SceneView.duringSceneGui += OnSceneGUI;
            Undo.undoRedoPerformed += OnUndoRedo;
        }

        private void OnDisable()
        {
            SceneView.duringSceneGui -= OnSceneGUI;
            Undo.undoRedoPerformed -= OnUndoRedo;
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            DrawHeader();
            DrawEditModeToggle();

            if (tilemap.IsEditMode)
            {
                DrawToolbar();
                DrawBrushPanel();
                DrawTileAssetPanel();
                DrawLayerPanel();
                DrawSettingsPanel();
                DrawStatistics();
            }
            else
            {
                DrawBasicSettings();
            }

            serializedObject.ApplyModifiedProperties();
        }

        /// <summary>
        /// Draw header with logo and title
        /// </summary>
        private void DrawHeader()
        {
            EditorGUILayout.BeginVertical("box");

            var headerStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 18,
                alignment = TextAnchor.MiddleCenter
            };

            EditorGUILayout.LabelField("🏗️ Tilemap3D Editor", headerStyle);
            EditorGUILayout.LabelField("Advanced 3D Tilemap System", EditorStyles.centeredGreyMiniLabel);

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Draw edit mode toggle
        /// </summary>
        private void DrawEditModeToggle()
        {
            EditorGUILayout.Space();
            EditorGUILayout.BeginHorizontal();

            var editModeContent = new GUIContent(
                tilemap.IsEditMode ? "🔧 Edit Mode: ON" : "⏸️ Edit Mode: OFF",
                "Toggle edit mode to start/stop editing tiles"
            );

            GUI.backgroundColor = tilemap.IsEditMode ? Color.green : Color.red;
            if (GUILayout.Button(editModeContent, GUILayout.Height(30)))
            {
                tilemap.SetEditMode(!tilemap.IsEditMode);
                EditorUtility.SetDirty(tilemap);
            }
            GUI.backgroundColor = Color.white;

            // Quick actions
            if (tilemap.IsEditMode)
            {
                if (GUILayout.Button("↶ Undo", GUILayout.Width(60), GUILayout.Height(30)))
                {
                    tilemap.Undo();
                }

                if (GUILayout.Button("↷ Redo", GUILayout.Width(60), GUILayout.Height(30)))
                {
                    tilemap.Redo();
                }
            }

            EditorGUILayout.EndHorizontal();
        }

        /// <summary>
        /// Draw toolbar with quick actions
        /// </summary>
        private void DrawToolbar()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("🛠️ Quick Actions", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("🗑️ Clear All", GUILayout.Height(25)))
            {
                if (EditorUtility.DisplayDialog("Clear All Tiles",
                    "Are you sure you want to clear all tiles? This cannot be undone.", "Yes", "No"))
                {
                    tilemap.ClearAllTiles();
                    EditorUtility.SetDirty(tilemap);
                }
            }

            if (GUILayout.Button("📊 Statistics", GUILayout.Height(25)))
            {
                ShowStatisticsWindow();
            }

            if (GUILayout.Button("💾 Save Template", GUILayout.Height(25)))
            {
                // TODO: Implement save template
                EditorUtility.DisplayDialog("Save Template", "Save template feature coming soon!", "OK");
            }

            if (GUILayout.Button("📁 Load Template", GUILayout.Height(25)))
            {
                // TODO: Implement load template
                EditorUtility.DisplayDialog("Load Template", "Load template feature coming soon!", "OK");
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Draw brush panel
        /// </summary>
        private void DrawBrushPanel()
        {
            EditorGUILayout.BeginVertical("box");

            showBrushes = EditorGUILayout.Foldout(showBrushes, "🖌️ Brushes", true);
            if (showBrushes)
            {
                // Brush type selection
                EditorGUILayout.LabelField("Brush Type:", EditorStyles.miniLabel);
                EditorGUILayout.BeginHorizontal();

                var brushTypes = brushManager.GetAvailableBrushTypes();
                int currentBrushIndex = System.Array.IndexOf(brushTypes, brushManager.ActiveBrushType);

                for (int i = 0; i < brushTypes.Length; i++)
                {
                    var brushType = brushTypes[i];
                    var icon = brushManager.GetBrushIcon(brushType);
                    var isSelected = i == currentBrushIndex;

                    GUI.backgroundColor = isSelected ? Color.green : Color.white;

                    var content = new GUIContent(icon, brushManager.GetBrushDisplayName(brushType));
                    if (GUILayout.Button(content, GUILayout.Width(30), GUILayout.Height(30)))
                    {
                        brushManager.SetActiveBrush(brushType);
                        tilemap.SetBrushType(brushType);
                        EditorUtility.SetDirty(tilemap);
                    }
                }

                GUI.backgroundColor = Color.white;
                EditorGUILayout.EndHorizontal();

                // Brush size
                EditorGUILayout.Space(5);
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Size:", GUILayout.Width(40));

                int newBrushSize = EditorGUILayout.IntSlider(brushManager.BrushSize, 1, 10);
                if (newBrushSize != brushManager.BrushSize)
                {
                    brushManager.SetBrushSize(newBrushSize);
                    tilemap.SetBrushSize(newBrushSize);
                    EditorUtility.SetDirty(tilemap);
                }

                EditorGUILayout.EndHorizontal();

                // Current brush info
                EditorGUILayout.Space(5);
                var activeBrushName = brushManager.GetBrushDisplayName(brushManager.ActiveBrushType);
                EditorGUILayout.LabelField($"Active: {activeBrushName}", EditorStyles.miniLabel);
            }

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Draw tile asset panel
        /// </summary>
        private void DrawTileAssetPanel()
        {
            EditorGUILayout.BeginVertical("box");

            showTileAssets = EditorGUILayout.Foldout(showTileAssets, "🧱 Tile Assets", true);
            if (showTileAssets)
            {
                // Search bar
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("🔍", GUILayout.Width(20));
                var searchText = EditorGUILayout.TextField("", GUILayout.ExpandWidth(true));
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(5);

                // Tile grid
                if (tilemap.TileAssets != null && tilemap.TileAssets.Count > 0)
                {
                    tileScrollPosition = EditorGUILayout.BeginScrollView(tileScrollPosition, GUILayout.Height(200));

                    int columns = Mathf.Max(1, Mathf.FloorToInt(EditorGUIUtility.currentViewWidth / (TILE_PREVIEW_SIZE + 10)));
                    int index = 0;

                    while (index < tilemap.TileAssets.Count)
                    {
                        EditorGUILayout.BeginHorizontal();

                        for (int i = 0; i < columns && index < tilemap.TileAssets.Count; i++, index++)
                        {
                            var tileAsset = tilemap.TileAssets[index];
                            if (tileAsset == null) continue;

                            DrawTileAssetButton(tileAsset, index);
                        }

                        EditorGUILayout.EndHorizontal();
                    }

                    EditorGUILayout.EndScrollView();
                }
                else
                {
                    EditorGUILayout.HelpBox("No tile assets found. Add some TileAsset ScriptableObjects to the list.", MessageType.Info);
                }

                // Add/Remove buttons
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("➕ Add Tile Asset"))
                {
                    // TODO: Open asset picker
                    EditorUtility.DisplayDialog("Add Tile Asset", "Asset picker coming soon!", "OK");
                }

                if (GUILayout.Button("➖ Remove Selected") && tilemap.SelectedTileAsset != null)
                {
                    tilemap.TileAssets.Remove(tilemap.SelectedTileAsset);
                    tilemap.SetSelectedTileAsset(null);
                    EditorUtility.SetDirty(tilemap);
                }
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Draw individual tile asset button
        /// </summary>
        private void DrawTileAssetButton(TileAsset tileAsset, int index)
        {
            var isSelected = tilemap.SelectedTileAsset == tileAsset;

            // Get preview texture
            Texture2D preview = null;
            if (tileAsset.Icon != null)
            {
                preview = tileAsset.Icon.texture;
            }
            else if (tileAsset.Prefab != null)
            {
                preview = AssetPreview.GetAssetPreview(tileAsset.Prefab) ?? AssetPreview.GetMiniThumbnail(tileAsset.Prefab);
            }

            // Button style
            var style = new GUIStyle(GUI.skin.button);
            if (isSelected)
            {
                style.normal.background = Texture2D.whiteTexture;
                GUI.backgroundColor = Color.green;
            }

            // Draw button
            var content = new GUIContent(preview, tileAsset.TileName);
            if (GUILayout.Button(content, style, GUILayout.Width(TILE_PREVIEW_SIZE), GUILayout.Height(TILE_PREVIEW_SIZE)))
            {
                tilemap.SetSelectedTileAsset(tileAsset);
                brushManager.SetSelectedTileAsset(tileAsset);
                EditorUtility.SetDirty(tilemap);
            }

            GUI.backgroundColor = Color.white;

            // Tile name below button
            var nameRect = GUILayoutUtility.GetLastRect();
            nameRect.y += TILE_PREVIEW_SIZE - 15;
            nameRect.height = 15;

            var nameStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                alignment = TextAnchor.MiddleCenter,
                fontSize = 9
            };

            GUI.Label(nameRect, tileAsset.TileName, nameStyle);
        }

        /// <summary>
        /// Draw layer panel
        /// </summary>
        private void DrawLayerPanel()
        {
            EditorGUILayout.BeginVertical("box");

            showLayers = EditorGUILayout.Foldout(showLayers, "📚 Layers", true);
            if (showLayers)
            {
                // Layer controls
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("➕ Add Layer"))
                {
                    tilemap.CreateLayer($"Layer {tilemap.Layers.Count + 1}");
                    EditorUtility.SetDirty(tilemap);
                }

                if (GUILayout.Button("➖ Remove Layer") && tilemap.Layers.Count > 1)
                {
                    if (EditorUtility.DisplayDialog("Remove Layer",
                        $"Remove layer '{tilemap.ActiveLayer?.LayerName}'?", "Yes", "No"))
                    {
                        tilemap.RemoveLayer(tilemap.ActiveLayerIndex);
                        EditorUtility.SetDirty(tilemap);
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(5);

                // Layer list
                layerScrollPosition = EditorGUILayout.BeginScrollView(layerScrollPosition, GUILayout.Height(150));

                for (int i = tilemap.Layers.Count - 1; i >= 0; i--) // Draw from top to bottom
                {
                    var layer = tilemap.Layers[i];
                    DrawLayerItem(layer, i);
                }

                EditorGUILayout.EndScrollView();
            }

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Draw individual layer item
        /// </summary>
        private void DrawLayerItem(TilemapLayer layer, int index)
        {
            var isActive = tilemap.ActiveLayerIndex == index;

            EditorGUILayout.BeginHorizontal("box");

            // Active indicator
            GUI.backgroundColor = isActive ? Color.green : Color.white;
            if (GUILayout.Button(isActive ? "●" : "○", GUILayout.Width(20), GUILayout.Height(LAYER_HEIGHT)))
            {
                tilemap.SetActiveLayer(index);
                EditorUtility.SetDirty(tilemap);
            }
            GUI.backgroundColor = Color.white;

            // Visibility toggle
            var visibilityIcon = layer.IsVisible ? "👁️" : "🙈";
            if (GUILayout.Button(visibilityIcon, GUILayout.Width(25), GUILayout.Height(LAYER_HEIGHT)))
            {
                layer.IsVisible = !layer.IsVisible;
                EditorUtility.SetDirty(tilemap);
            }

            // Lock toggle
            var lockIcon = layer.IsLocked ? "🔒" : "🔓";
            if (GUILayout.Button(lockIcon, GUILayout.Width(25), GUILayout.Height(LAYER_HEIGHT)))
            {
                layer.IsLocked = !layer.IsLocked;
                EditorUtility.SetDirty(tilemap);
            }

            // Layer name
            var newName = EditorGUILayout.TextField(layer.LayerName, GUILayout.Height(LAYER_HEIGHT));
            if (newName != layer.LayerName)
            {
                layer.LayerName = newName;
                EditorUtility.SetDirty(tilemap);
            }

            // Tile count
            EditorGUILayout.LabelField($"({layer.TileCount})", GUILayout.Width(40), GUILayout.Height(LAYER_HEIGHT));

            EditorGUILayout.EndHorizontal();

            // Layer properties (when active)
            if (isActive)
            {
                EditorGUI.indentLevel++;

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Opacity:", GUILayout.Width(50));
                var newOpacity = EditorGUILayout.Slider(layer.Opacity, 0f, 1f);
                if (newOpacity != layer.Opacity)
                {
                    layer.Opacity = newOpacity;
                    EditorUtility.SetDirty(tilemap);
                }
                EditorGUILayout.EndHorizontal();

                EditorGUI.indentLevel--;
            }
        }

        /// <summary>
        /// Draw settings panel
        /// </summary>
        private void DrawSettingsPanel()
        {
            EditorGUILayout.BeginVertical("box");

            showSettings = EditorGUILayout.Foldout(showSettings, "⚙️ Settings", true);
            if (showSettings)
            {
                // Grid settings
                EditorGUILayout.LabelField("Grid Settings", EditorStyles.boldLabel);

                var showGrid = EditorGUILayout.Toggle("Show Grid", tilemap.ShowGrid);
                if (showGrid != tilemap.ShowGrid)
                {
                    // Update show grid property
                    EditorUtility.SetDirty(tilemap);
                }

                // Performance settings
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Performance", EditorStyles.boldLabel);

                var enableChunking = EditorGUILayout.Toggle("Enable Chunking", tilemap.EnableChunking);
                if (enableChunking != tilemap.EnableChunking)
                {
                    EditorUtility.SetDirty(tilemap);
                }
            }

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Draw basic settings when not in edit mode
        /// </summary>
        private void DrawBasicSettings()
        {
            EditorGUILayout.HelpBox("Enable Edit Mode to access advanced tilemap editing features.", MessageType.Info);
            DrawDefaultInspector();
        }

        /// <summary>
        /// Draw statistics
        /// </summary>
        private void DrawStatistics()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📊 Statistics", EditorStyles.boldLabel);

            int totalTiles = 0;
            foreach (var layer in tilemap.Layers)
            {
                totalTiles += layer.TileCount;
            }

            EditorGUILayout.LabelField($"Total Tiles: {totalTiles}");
            EditorGUILayout.LabelField($"Layers: {tilemap.Layers.Count}");
            EditorGUILayout.LabelField($"Active Layer: {tilemap.ActiveLayer?.LayerName ?? "None"}");
            EditorGUILayout.LabelField($"Selected Tile: {tilemap.SelectedTileAsset?.TileName ?? "None"}");

            if (tilemap.History != null)
            {
                EditorGUILayout.LabelField($"Undo Stack: {tilemap.History.UndoCount}");
                EditorGUILayout.LabelField($"Redo Stack: {tilemap.History.RedoCount}");
            }

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Handle Scene GUI
        /// </summary>
        private void OnSceneGUI(SceneView sceneView)
        {
            if (!tilemap || !tilemap.IsEditMode) return;

            HandleInput();
            DrawGridOverlay();
            DrawBrushPreview();
            DrawTileHighlight();
        }

        /// <summary>
        /// Handle input in scene view
        /// </summary>
        private void HandleInput()
        {
            Event e = Event.current;
            if (e == null) return;

            // Get mouse position in world
            Ray ray = HandleUtility.GUIPointToWorldRay(e.mousePosition);
            Plane groundPlane = new Plane(Vector3.up, Vector3.zero);

            if (groundPlane.Raycast(ray, out float distance))
            {
                Vector3 worldPosition = ray.GetPoint(distance);
                Vector3Int gridPosition = tilemap.WorldToGrid(worldPosition);

                // Update hover position
                if (gridPosition != lastHoverPosition)
                {
                    lastHoverPosition = gridPosition;
                    SceneView.RepaintAll();
                }

                // Handle mouse input
                if (e.type == EventType.MouseDown && e.button == 0)
                {
                    isMouseDown = true;
                    HandlePaintInput(gridPosition, e);
                    e.Use();
                }
                else if (e.type == EventType.MouseDrag && e.button == 0 && isMouseDown)
                {
                    HandlePaintInput(gridPosition, e);
                    e.Use();
                }
                else if (e.type == EventType.MouseUp && e.button == 0)
                {
                    isMouseDown = false;
                }

                // Handle right click for erasing
                if (e.type == EventType.MouseDown && e.button == 1)
                {
                    HandleEraseInput(gridPosition, e);
                    e.Use();
                }

                // Handle keyboard shortcuts
                if (e.type == EventType.KeyDown)
                {
                    HandleKeyboardInput(e);
                }
            }
        }

        /// <summary>
        /// Handle paint input
        /// </summary>
        private void HandlePaintInput(Vector3Int gridPosition, Event e)
        {
            if (e.alt) return; // Don't paint when alt is held (camera control)

            brushManager.Paint(tilemap, gridPosition);
            EditorUtility.SetDirty(tilemap);
        }

        /// <summary>
        /// Handle erase input
        /// </summary>
        private void HandleEraseInput(Vector3Int gridPosition, Event e)
        {
            if (e.alt) return;

            brushManager.Erase(tilemap, gridPosition);
            EditorUtility.SetDirty(tilemap);
        }

        /// <summary>
        /// Handle keyboard input
        /// </summary>
        private void HandleKeyboardInput(Event e)
        {
            switch (e.keyCode)
            {
                case KeyCode.Z:
                    if (e.control)
                    {
                        if (e.shift)
                            tilemap.Redo();
                        else
                            tilemap.Undo();
                        e.Use();
                    }
                    break;

                case KeyCode.Alpha1:
                    brushManager.SetActiveBrush(BrushType.Single);
                    e.Use();
                    break;

                case KeyCode.Alpha2:
                    brushManager.SetActiveBrush(BrushType.Rectangle);
                    e.Use();
                    break;

                case KeyCode.Alpha3:
                    brushManager.SetActiveBrush(BrushType.Circle);
                    e.Use();
                    break;

                case KeyCode.I:
                    brushManager.SetActiveBrush(BrushType.Eyedropper);
                    e.Use();
                    break;

                case KeyCode.E:
                    brushManager.SetActiveBrush(BrushType.Eraser);
                    e.Use();
                    break;
            }
        }

        /// <summary>
        /// Draw grid overlay
        /// </summary>
        private void DrawGridOverlay()
        {
            if (!tilemap.ShowGrid) return;

            Handles.color = tilemap.GridColor;

            // Draw grid lines around camera view
            var camera = SceneView.currentDrawingSceneView.camera;
            if (camera == null) return;

            Vector3 cameraPos = camera.transform.position;
            int gridRange = 20; // Draw 20x20 grid around camera

            for (int x = -gridRange; x <= gridRange; x++)
            {
                Vector3 start = new Vector3(x, 0, -gridRange) + cameraPos;
                Vector3 end = new Vector3(x, 0, gridRange) + cameraPos;
                start = tilemap.GridToWorld(tilemap.WorldToGrid(start));
                end = tilemap.GridToWorld(tilemap.WorldToGrid(end));
                Handles.DrawLine(start, end);
            }

            for (int z = -gridRange; z <= gridRange; z++)
            {
                Vector3 start = new Vector3(-gridRange, 0, z) + cameraPos;
                Vector3 end = new Vector3(gridRange, 0, z) + cameraPos;
                start = tilemap.GridToWorld(tilemap.WorldToGrid(start));
                end = tilemap.GridToWorld(tilemap.WorldToGrid(end));
                Handles.DrawLine(start, end);
            }
        }

        /// <summary>
        /// Draw brush preview
        /// </summary>
        private void DrawBrushPreview()
        {
            var previewPositions = brushManager.GetPreviewPositions(lastHoverPosition);

            Handles.color = Color.yellow;
            foreach (var pos in previewPositions)
            {
                Vector3 worldPos = tilemap.GridToWorld(pos);
                Handles.DrawWireCube(worldPos + Vector3.up * 0.5f, Vector3.one);
            }
        }

        /// <summary>
        /// Draw tile highlight
        /// </summary>
        private void DrawTileHighlight()
        {
            Vector3 worldPos = tilemap.GridToWorld(lastHoverPosition);

            // Highlight current cell
            Handles.color = Color.green;
            Handles.DrawWireCube(worldPos + Vector3.up * 0.5f, Vector3.one * 1.1f);

            // Show coordinates
            var style = new GUIStyle();
            style.normal.textColor = Color.white;
            style.fontSize = 12;

            Handles.Label(worldPos + Vector3.up * 1.5f, $"{lastHoverPosition.x}, {lastHoverPosition.y}, {lastHoverPosition.z}", style);
        }

        /// <summary>
        /// Handle tile selection from brush manager
        /// </summary>
        private void OnTileSelected(TileAsset tileAsset)
        {
            tilemap.SetSelectedTileAsset(tileAsset);
            EditorUtility.SetDirty(tilemap);
        }

        /// <summary>
        /// Handle undo/redo
        /// </summary>
        private void OnUndoRedo()
        {
            if (tilemap != null)
            {
                EditorUtility.SetDirty(tilemap);
                SceneView.RepaintAll();
            }
        }

        /// <summary>
        /// Show statistics window
        /// </summary>
        private void ShowStatisticsWindow()
        {
            var bounds = tilemap.GetBounds();
            int totalTiles = 0;

            foreach (var layer in tilemap.Layers)
            {
                totalTiles += layer.TileCount;
            }

            string stats = $@"=== TILEMAP STATISTICS ===

Total Tiles: {totalTiles}
Layers: {tilemap.Layers.Count}
Bounds: {bounds.size}
Grid Size: {tilemap.GridSize}
Cell Size: {tilemap.CellSize}

Active Layer: {tilemap.ActiveLayer?.LayerName ?? "None"}
Selected Tile: {tilemap.SelectedTileAsset?.TileName ?? "None"}
Active Brush: {brushManager.GetBrushDisplayName(brushManager.ActiveBrushType)}

History:
- Undo Stack: {tilemap.History?.UndoCount ?? 0}
- Redo Stack: {tilemap.History?.RedoCount ?? 0}

Performance:
- Chunking: {(tilemap.EnableChunking ? "Enabled" : "Disabled")}
- Chunk Size: {tilemap.ChunkSize}";

            EditorUtility.DisplayDialog("Tilemap Statistics", stats, "OK");
        }

        /// <summary>
        /// Create menu item for opening tilemap editor window
        /// </summary>
        [MenuItem("Window/Tilemap3D/Editor Window")]
        public static void OpenEditorWindow()
        {
            var window = EditorWindow.GetWindow<Tilemap3DEditorWindow>("Tilemap3D Editor");
            window.Show();
        }
    }
}
