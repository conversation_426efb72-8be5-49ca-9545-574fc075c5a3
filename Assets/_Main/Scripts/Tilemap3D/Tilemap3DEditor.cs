using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(Tilemap3D))]
public class Tilemap3DEditor : Editor
{
    private Tilemap3D _map;
    private const int tileSize = 64;

    private void OnEnable()
    {
        _map = (Tilemap3D)target;
        SceneView.duringSceneGui += OnSceneGUI;
    }

    private void OnDisable()
    {
        SceneView.duringSceneGui -= OnSceneGUI;
    }

    private void OnSceneGUI(SceneView sceneView)
    {
        if (!_map || !_map.IsEditMode) return;

        DrawMouseHighlight();
        HandleInput();
    }

    private void HandleInput()
    {
        Event e = Event.current;
        if (e == null || e.alt || e.type != EventType.MouseDown || e.button != 0) return;

        Ray ray = HandleUtility.GUIPointToWorldRay(e.mousePosition);
        if (!Physics.Raycast(ray, out RaycastHit hit)) return;

        if (_map.Modes[1].isActive)
        {
            _map.PlaceTileAtWorldPosition(hit.point);
            e.Use();
        }
        else if (_map.Modes[2].isActive)
        {
            _map.RemoveTileAtWorldPosition(hit.point);
            e.Use();
        }
    }

    private void DrawMouseHighlight()
    {
        Event e = Event.current;
        Ray ray = HandleUtility.GUIPointToWorldRay(e.mousePosition);

        if (Physics.Raycast(ray, out RaycastHit hit))
        {
            Vector3Int cell = Vector3Int.FloorToInt(hit.point);
            Vector3 center = cell + new Vector3(0.5f, 0, 0.5f);

            Handles.color = Color.green;
            Handles.DrawWireCube(center, Vector3.one);

            SceneView.RepaintAll();
        }
    }

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        DrawEditModeToggle();
        if (!_map.IsEditMode) return;
        DrawModeButtons();
        DrawPrefabPicker();
    }

    private void DrawEditModeToggle()
    {
        EditorGUILayout.Space();
        GUI.backgroundColor = _map.IsEditMode ? Color.green : Color.red;
        if (GUILayout.Button(_map.IsEditMode ? "Disable Edit Mode" : "Enable Edit Mode"))
        {
            _map.IsEditMode = !_map.IsEditMode;
        }
        GUI.backgroundColor = Color.white;
    }

    private void DrawModeButtons()
    {

        EditorGUILayout.BeginHorizontal();
        for (int i = 0; i < _map.Modes.Length; i++)
        {
            GUI.backgroundColor = _map.Modes[i].isActive ? Color.green : Color.red;
            if (GUILayout.Button(_map.Modes[i].name))
            {
                _map.SelectMode(i);
            }
        }
        GUI.backgroundColor = Color.white;
        EditorGUILayout.EndHorizontal();
    }

    private void DrawPrefabPicker()
    {
        if (_map.TilePrefabs == null) return;

        EditorGUILayout.LabelField("Select Tile", EditorStyles.boldLabel);
        int columns = Mathf.FloorToInt(EditorGUIUtility.currentViewWidth / (tileSize + 10));
        int index = 0;

        EditorGUILayout.BeginVertical();
        while (index < _map.TilePrefabs.Length)
        {
            EditorGUILayout.BeginHorizontal();
            for (int i = 0; i < columns && index < _map.TilePrefabs.Length; i++, index++)
            {
                GameObject prefab = _map.TilePrefabs[index];
                if (prefab == null) continue;

                Texture2D preview = AssetPreview.GetAssetPreview(prefab) ?? AssetPreview.GetMiniThumbnail(prefab);

                GUIStyle style = new GUIStyle(GUI.skin.button);
                if (index == _map.SelectedTileIndex)
                {
                    style.normal.background = Texture2D.whiteTexture;
                    style.normal.textColor = Color.green;
                }

                if (GUILayout.Button(preview, style, GUILayout.Width(tileSize), GUILayout.Height(tileSize)))
                {
                    _map.SelectedTileIndex = index;
                    GUI.FocusControl(null);
                    EditorUtility.SetDirty(_map);
                }
            }
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndVertical();

        serializedObject.ApplyModifiedProperties();
    }
}
