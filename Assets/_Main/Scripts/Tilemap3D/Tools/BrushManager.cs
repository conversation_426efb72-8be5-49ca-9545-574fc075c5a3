using System.Collections.Generic;
using UnityEngine;
using Tilemap3D.Core;

namespace Tilemap3D.Tools
{
    /// <summary>
    /// Manages different brush types and handles brush operations
    /// </summary>
    public class BrushManager
    {
        private Dictionary<BrushType, TilemapBrush> brushes = new Dictionary<BrushType, TilemapBrush>();
        private BrushType activeBrushType = BrushType.Single;
        private TileAsset selectedTileAsset;
        private int brushSize = 1;
        
        // Events
        public System.Action<BrushType> OnBrushChanged;
        public System.Action<TileAsset> OnTileSelected;
        public System.Action<int> OnBrushSizeChanged;
        
        // Properties
        public BrushType ActiveBrushType => activeBrushType;
        public TilemapBrush ActiveBrush => brushes.TryGetValue(activeBrushType, out var brush) ? brush : null;
        public TileAsset SelectedTileAsset => selectedTileAsset;
        public int BrushSize => brushSize;
        
        /// <summary>
        /// Initialize brush manager
        /// </summary>
        public void Initialize()
        {
            // Create all brush types
            brushes[BrushType.Single] = new SingleBrush();
            brushes[BrushType.Rectangle] = new RectangleBrush();
            brushes[BrushType.Circle] = new CircleBrush();
            brushes[BrushType.Line] = new LineBrush();
            brushes[BrushType.FloodFill] = new FloodFillBrush();
            brushes[BrushType.Eyedropper] = new EyedropperBrush();
            brushes[BrushType.Eraser] = new EraserBrush();
            
            // Setup eyedropper callback
            if (brushes[BrushType.Eyedropper] is EyedropperBrush eyedropper)
            {
                eyedropper.OnTilePicked += SetSelectedTileAsset;
            }
            
            // Set initial brush properties
            UpdateBrushProperties();
        }
        
        /// <summary>
        /// Set active brush type
        /// </summary>
        public void SetActiveBrush(BrushType brushType)
        {
            if (activeBrushType == brushType) return;
            
            activeBrushType = brushType;
            UpdateBrushProperties();
            OnBrushChanged?.Invoke(brushType);
        }
        
        /// <summary>
        /// Set selected tile asset
        /// </summary>
        public void SetSelectedTileAsset(TileAsset tileAsset)
        {
            selectedTileAsset = tileAsset;
            UpdateBrushProperties();
            OnTileSelected?.Invoke(tileAsset);
        }
        
        /// <summary>
        /// Set brush size
        /// </summary>
        public void SetBrushSize(int size)
        {
            brushSize = Mathf.Max(1, size);
            UpdateBrushProperties();
            OnBrushSizeChanged?.Invoke(brushSize);
        }
        
        /// <summary>
        /// Paint with active brush
        /// </summary>
        public void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex = -1)
        {
            var brush = ActiveBrush;
            if (brush == null) return;
            
            if (layerIndex == -1) layerIndex = tilemap.ActiveLayerIndex;
            brush.Paint(tilemap, position, layerIndex);
        }
        
        /// <summary>
        /// Erase with active brush
        /// </summary>
        public void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex = -1)
        {
            var brush = ActiveBrush;
            if (brush == null) return;
            
            if (layerIndex == -1) layerIndex = tilemap.ActiveLayerIndex;
            brush.Erase(tilemap, position, layerIndex);
        }
        
        /// <summary>
        /// Get preview positions for active brush
        /// </summary>
        public List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            var brush = ActiveBrush;
            if (brush == null) return new List<Vector3Int>();
            
            return brush.GetPreviewPositions(centerPosition);
        }
        
        /// <summary>
        /// Check if can paint at position
        /// </summary>
        public bool CanPaint(Tilemap3D tilemap, Vector3Int position, int layerIndex = -1)
        {
            var brush = ActiveBrush;
            if (brush == null) return false;
            
            if (layerIndex == -1) layerIndex = tilemap.ActiveLayerIndex;
            return brush.CanPaint(tilemap, position, layerIndex);
        }
        
        /// <summary>
        /// Update brush properties
        /// </summary>
        private void UpdateBrushProperties()
        {
            foreach (var brush in brushes.Values)
            {
                brush.SetSelectedTile(selectedTileAsset);
                brush.SetBrushSize(brushSize);
            }
        }
        
        /// <summary>
        /// Get brush by type
        /// </summary>
        public TilemapBrush GetBrush(BrushType brushType)
        {
            brushes.TryGetValue(brushType, out var brush);
            return brush;
        }
        
        /// <summary>
        /// Get all available brush types
        /// </summary>
        public BrushType[] GetAvailableBrushTypes()
        {
            return new BrushType[]
            {
                BrushType.Single,
                BrushType.Rectangle,
                BrushType.Circle,
                BrushType.Line,
                BrushType.FloodFill,
                BrushType.Eyedropper,
                BrushType.Eraser
            };
        }
        
        /// <summary>
        /// Get brush display name
        /// </summary>
        public string GetBrushDisplayName(BrushType brushType)
        {
            return brushType switch
            {
                BrushType.Single => "Single Tile",
                BrushType.Rectangle => "Rectangle",
                BrushType.Circle => "Circle",
                BrushType.Line => "Line",
                BrushType.FloodFill => "Flood Fill",
                BrushType.Eyedropper => "Eyedropper",
                BrushType.Eraser => "Eraser",
                _ => brushType.ToString()
            };
        }
        
        /// <summary>
        /// Get brush icon (you can implement this to return actual icons)
        /// </summary>
        public string GetBrushIcon(BrushType brushType)
        {
            return brushType switch
            {
                BrushType.Single => "🖌️",
                BrushType.Rectangle => "⬜",
                BrushType.Circle => "⭕",
                BrushType.Line => "📏",
                BrushType.FloodFill => "🌊",
                BrushType.Eyedropper => "💧",
                BrushType.Eraser => "🧽",
                _ => "🔧"
            };
        }
    }
    
    /// <summary>
    /// Simple brush implementations for the manager
    /// </summary>
    public abstract partial class TilemapBrush
    {
        protected BrushType brushType;
        protected int brushSize = 1;
        protected TileAsset selectedTile;
        
        public BrushType BrushType => brushType;
        public int BrushSize => brushSize;
        public TileAsset SelectedTile => selectedTile;
        
        protected TilemapBrush(BrushType type)
        {
            brushType = type;
        }
        
        public virtual void SetBrushSize(int size)
        {
            brushSize = Mathf.Max(1, size);
        }
        
        public virtual void SetSelectedTile(TileAsset tile)
        {
            selectedTile = tile;
        }
        
        public abstract void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex);
        public abstract void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex);
        public abstract List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition);
        
        public virtual bool CanPaint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (selectedTile == null && brushType != BrushType.Eraser && brushType != BrushType.Eyedropper) return false;
            if (layerIndex < 0 || layerIndex >= tilemap.Layers.Count) return false;
            if (tilemap.Layers[layerIndex].IsLocked) return false;
            return true;
        }
    }
    
    // Simple brush implementations
    public partial class SingleBrush : TilemapBrush
    {
        public SingleBrush() : base(BrushType.Single) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            tilemap.PlaceTile(position, selectedTile, layerIndex);
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            tilemap.RemoveTile(position, layerIndex);
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            return new List<Vector3Int> { centerPosition };
        }
    }
    
    public partial class RectangleBrush : TilemapBrush
    {
        public RectangleBrush() : base(BrushType.Rectangle) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            
            var positions = GetPreviewPositions(position);
            foreach (var pos in positions)
            {
                tilemap.PlaceTile(pos, selectedTile, layerIndex);
            }
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var positions = GetPreviewPositions(position);
            foreach (var pos in positions)
            {
                tilemap.RemoveTile(pos, layerIndex);
            }
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            var positions = new List<Vector3Int>();
            int halfSize = brushSize / 2;
            
            for (int x = -halfSize; x <= halfSize; x++)
            {
                for (int z = -halfSize; z <= halfSize; z++)
                {
                    positions.Add(centerPosition + new Vector3Int(x, 0, z));
                }
            }
            
            return positions;
        }
    }
    
    public partial class CircleBrush : TilemapBrush
    {
        public CircleBrush() : base(BrushType.Circle) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            
            var positions = GetPreviewPositions(position);
            foreach (var pos in positions)
            {
                tilemap.PlaceTile(pos, selectedTile, layerIndex);
            }
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var positions = GetPreviewPositions(position);
            foreach (var pos in positions)
            {
                tilemap.RemoveTile(pos, layerIndex);
            }
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            var positions = new List<Vector3Int>();
            float radius = brushSize * 0.5f;
            int intRadius = Mathf.CeilToInt(radius);
            
            for (int x = -intRadius; x <= intRadius; x++)
            {
                for (int z = -intRadius; z <= intRadius; z++)
                {
                    float distance = Mathf.Sqrt(x * x + z * z);
                    if (distance <= radius)
                    {
                        positions.Add(centerPosition + new Vector3Int(x, 0, z));
                    }
                }
            }
            
            return positions;
        }
    }
    
    public partial class LineBrush : TilemapBrush
    {
        public LineBrush() : base(BrushType.Line) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            tilemap.PlaceTile(position, selectedTile, layerIndex);
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            tilemap.RemoveTile(position, layerIndex);
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            return new List<Vector3Int> { centerPosition };
        }
    }
    
    public class FloodFillBrush : TilemapBrush
    {
        public FloodFillBrush() : base(BrushType.FloodFill) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            // Simplified flood fill - just place single tile for now
            tilemap.PlaceTile(position, selectedTile, layerIndex);
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            tilemap.RemoveTile(position, layerIndex);
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            return new List<Vector3Int> { centerPosition };
        }
    }
    
    public class EyedropperBrush : TilemapBrush
    {
        public System.Action<TileAsset> OnTilePicked;
        
        public EyedropperBrush() : base(BrushType.Eyedropper) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var tile = tilemap.GetTile(position, layerIndex);
            if (tile != null && tile.TileAsset != null)
            {
                selectedTile = tile.TileAsset;
                OnTilePicked?.Invoke(tile.TileAsset);
            }
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            Paint(tilemap, position, layerIndex);
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            return new List<Vector3Int> { centerPosition };
        }
    }
    
    public class EraserBrush : TilemapBrush
    {
        public EraserBrush() : base(BrushType.Eraser) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            Erase(tilemap, position, layerIndex);
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var positions = GetPreviewPositions(position);
            foreach (var pos in positions)
            {
                tilemap.RemoveTile(pos, layerIndex);
            }
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            var positions = new List<Vector3Int>();
            int halfSize = brushSize / 2;
            
            for (int x = -halfSize; x <= halfSize; x++)
            {
                for (int z = -halfSize; z <= halfSize; z++)
                {
                    positions.Add(centerPosition + new Vector3Int(x, 0, z));
                }
            }
            
            return positions;
        }
    }
}
