using System.Collections.Generic;
using UnityEngine;
using Tilemap3D.Core;

namespace Tilemap3D.Tools
{
    /// <summary>
    /// Manages different brush types and handles brush operations
    /// </summary>
    public class BrushManager
    {
        private Dictionary<BrushType, TilemapBrush> brushes = new Dictionary<BrushType, TilemapBrush>();
        private BrushType activeBrushType = BrushType.Single;
        private TileAsset selectedTileAsset;
        private int brushSize = 1;

        // Events
        public System.Action<BrushType> OnBrushChanged;
        public System.Action<TileAsset> OnTileSelected;
        public System.Action<int> OnBrushSizeChanged;

        // Properties
        public BrushType ActiveBrushType => activeBrushType;
        public TilemapBrush ActiveBrush => brushes.TryGetValue(activeBrushType, out var brush) ? brush : null;
        public TileAsset SelectedTileAsset => selectedTileAsset;
        public int BrushSize => brushSize;

        /// <summary>
        /// Initialize brush manager
        /// </summary>
        public void Initialize()
        {
            // Create all brush types
            brushes[BrushType.Single] = new SingleBrush();
            brushes[BrushType.Rectangle] = new RectangleBrush();
            brushes[BrushType.Circle] = new CircleBrush();
            brushes[BrushType.Line] = new LineBrush();
            brushes[BrushType.FloodFill] = new FloodFillBrush();
            brushes[BrushType.Eyedropper] = new EyedropperBrush();
            brushes[BrushType.Eraser] = new EraserBrush();

            // Setup eyedropper callback
            if (brushes[BrushType.Eyedropper] is EyedropperBrush eyedropper)
            {
                eyedropper.OnTilePicked += SetSelectedTileAsset;
            }

            // Set initial brush properties
            UpdateBrushProperties();
        }

        /// <summary>
        /// Set active brush type
        /// </summary>
        public void SetActiveBrush(BrushType brushType)
        {
            if (activeBrushType == brushType) return;

            activeBrushType = brushType;
            UpdateBrushProperties();
            OnBrushChanged?.Invoke(brushType);
        }

        /// <summary>
        /// Set selected tile asset
        /// </summary>
        public void SetSelectedTileAsset(TileAsset tileAsset)
        {
            selectedTileAsset = tileAsset;
            UpdateBrushProperties();
            OnTileSelected?.Invoke(tileAsset);
        }

        /// <summary>
        /// Set brush size
        /// </summary>
        public void SetBrushSize(int size)
        {
            brushSize = Mathf.Max(1, size);
            UpdateBrushProperties();
            OnBrushSizeChanged?.Invoke(brushSize);
        }

        /// <summary>
        /// Paint with active brush
        /// </summary>
        public void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex = -1)
        {
            var brush = ActiveBrush;
            if (brush == null) return;

            if (layerIndex == -1) layerIndex = tilemap.ActiveLayerIndex;
            brush.Paint(tilemap, position, layerIndex);
        }

        /// <summary>
        /// Erase with active brush
        /// </summary>
        public void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex = -1)
        {
            var brush = ActiveBrush;
            if (brush == null) return;

            if (layerIndex == -1) layerIndex = tilemap.ActiveLayerIndex;
            brush.Erase(tilemap, position, layerIndex);
        }

        /// <summary>
        /// Get preview positions for active brush
        /// </summary>
        public List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            var brush = ActiveBrush;
            if (brush == null) return new List<Vector3Int>();

            return brush.GetPreviewPositions(centerPosition);
        }

        /// <summary>
        /// Check if can paint at position
        /// </summary>
        public bool CanPaint(Tilemap3D tilemap, Vector3Int position, int layerIndex = -1)
        {
            var brush = ActiveBrush;
            if (brush == null) return false;

            if (layerIndex == -1) layerIndex = tilemap.ActiveLayerIndex;
            return brush.CanPaint(tilemap, position, layerIndex);
        }

        /// <summary>
        /// Update brush properties
        /// </summary>
        private void UpdateBrushProperties()
        {
            foreach (var brush in brushes.Values)
            {
                brush.SetSelectedTile(selectedTileAsset);
                brush.SetBrushSize(brushSize);
            }
        }

        /// <summary>
        /// Get brush by type
        /// </summary>
        public TilemapBrush GetBrush(BrushType brushType)
        {
            brushes.TryGetValue(brushType, out var brush);
            return brush;
        }

        /// <summary>
        /// Get all available brush types
        /// </summary>
        public BrushType[] GetAvailableBrushTypes()
        {
            return new BrushType[]
            {
                BrushType.Single,
                BrushType.Rectangle,
                BrushType.Circle,
                BrushType.Line,
                BrushType.FloodFill,
                BrushType.Eyedropper,
                BrushType.Eraser
            };
        }

        /// <summary>
        /// Get brush display name
        /// </summary>
        public string GetBrushDisplayName(BrushType brushType)
        {
            return brushType switch
            {
                BrushType.Single => "Single Tile",
                BrushType.Rectangle => "Rectangle",
                BrushType.Circle => "Circle",
                BrushType.Line => "Line",
                BrushType.FloodFill => "Flood Fill",
                BrushType.Eyedropper => "Eyedropper",
                BrushType.Eraser => "Eraser",
                _ => brushType.ToString()
            };
        }

        /// <summary>
        /// Get brush icon
        /// </summary>
        public string GetBrushIcon(BrushType brushType)
        {
            return brushType switch
            {
                BrushType.Single => "🖌️",
                BrushType.Rectangle => "⬜",
                BrushType.Circle => "⭕",
                BrushType.Line => "📏",
                BrushType.FloodFill => "🌊",
                BrushType.Eyedropper => "💧",
                BrushType.Eraser => "🧽",
                _ => "🔧"
            };
        }
    }
}
