using System.Collections.Generic;
using UnityEngine;
using Tilemap3D.Core;

namespace Tilemap3D.Tools
{
    /// <summary>
    /// Base class for tilemap brushes
    /// </summary>
    public abstract class TilemapBrush
    {
        protected BrushType brushType;
        protected int brushSize = 1;
        protected TileAsset selectedTile;
        
        // Properties
        public BrushType BrushType => brushType;
        public int BrushSize => brushSize;
        public TileAsset SelectedTile => selectedTile;
        
        /// <summary>
        /// Constructor
        /// </summary>
        protected TilemapBrush(BrushType type)
        {
            brushType = type;
        }
        
        /// <summary>
        /// Set brush size
        /// </summary>
        public virtual void SetBrushSize(int size)
        {
            brushSize = Mathf.Max(1, size);
        }
        
        /// <summary>
        /// Set selected tile
        /// </summary>
        public virtual void SetSelectedTile(TileAsset tile)
        {
            selectedTile = tile;
        }
        
        /// <summary>
        /// Paint tiles at position
        /// </summary>
        public abstract void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex);
        
        /// <summary>
        /// Erase tiles at position
        /// </summary>
        public abstract void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex);
        
        /// <summary>
        /// Get preview positions for brush
        /// </summary>
        public abstract List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition);
        
        /// <summary>
        /// Check if brush can paint at position
        /// </summary>
        public virtual bool CanPaint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (selectedTile == null) return false;
            if (layerIndex < 0 || layerIndex >= tilemap.Layers.Count) return false;
            if (tilemap.Layers[layerIndex].IsLocked) return false;
            
            return true;
        }
    }
    
    /// <summary>
    /// Single tile brush
    /// </summary>
    public class SingleBrush : TilemapBrush
    {
        public SingleBrush() : base(BrushType.Single) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            tilemap.PlaceTile(position, selectedTile, layerIndex);
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            tilemap.RemoveTile(position, layerIndex);
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            return new List<Vector3Int> { centerPosition };
        }
    }
    
    /// <summary>
    /// Rectangle brush
    /// </summary>
    public class RectangleBrush : TilemapBrush
    {
        public RectangleBrush() : base(BrushType.Rectangle) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            
            var positions = GetPreviewPositions(position);
            var changes = new List<TileChangeData>();
            
            foreach (var pos in positions)
            {
                var existingTile = tilemap.GetTile(pos, layerIndex);
                changes.Add(new TileChangeData
                {
                    position = pos,
                    layerIndex = layerIndex,
                    oldTileAsset = existingTile?.TileAsset,
                    newTileAsset = selectedTile,
                    changeType = TileChangeType.Place
                });
                
                tilemap.PlaceTile(pos, selectedTile, layerIndex);
            }
            
            tilemap.History.RecordBatchChanges(changes);
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var positions = GetPreviewPositions(position);
            var changes = new List<TileChangeData>();
            
            foreach (var pos in positions)
            {
                var existingTile = tilemap.GetTile(pos, layerIndex);
                if (existingTile != null)
                {
                    changes.Add(new TileChangeData
                    {
                        position = pos,
                        layerIndex = layerIndex,
                        oldTileAsset = existingTile.TileAsset,
                        newTileAsset = null,
                        changeType = TileChangeType.Remove
                    });
                    
                    tilemap.RemoveTile(pos, layerIndex);
                }
            }
            
            if (changes.Count > 0)
            {
                tilemap.History.RecordBatchChanges(changes);
            }
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            var positions = new List<Vector3Int>();
            int halfSize = brushSize / 2;
            
            for (int x = -halfSize; x <= halfSize; x++)
            {
                for (int z = -halfSize; z <= halfSize; z++)
                {
                    positions.Add(centerPosition + new Vector3Int(x, 0, z));
                }
            }
            
            return positions;
        }
    }
    
    /// <summary>
    /// Circle brush
    /// </summary>
    public class CircleBrush : TilemapBrush
    {
        public CircleBrush() : base(BrushType.Circle) { }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            
            var positions = GetPreviewPositions(position);
            var changes = new List<TileChangeData>();
            
            foreach (var pos in positions)
            {
                var existingTile = tilemap.GetTile(pos, layerIndex);
                changes.Add(new TileChangeData
                {
                    position = pos,
                    layerIndex = layerIndex,
                    oldTileAsset = existingTile?.TileAsset,
                    newTileAsset = selectedTile,
                    changeType = TileChangeType.Place
                });
                
                tilemap.PlaceTile(pos, selectedTile, layerIndex);
            }
            
            tilemap.History.RecordBatchChanges(changes);
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var positions = GetPreviewPositions(position);
            var changes = new List<TileChangeData>();
            
            foreach (var pos in positions)
            {
                var existingTile = tilemap.GetTile(pos, layerIndex);
                if (existingTile != null)
                {
                    changes.Add(new TileChangeData
                    {
                        position = pos,
                        layerIndex = layerIndex,
                        oldTileAsset = existingTile.TileAsset,
                        newTileAsset = null,
                        changeType = TileChangeType.Remove
                    });
                    
                    tilemap.RemoveTile(pos, layerIndex);
                }
            }
            
            if (changes.Count > 0)
            {
                tilemap.History.RecordBatchChanges(changes);
            }
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            var positions = new List<Vector3Int>();
            float radius = brushSize * 0.5f;
            int intRadius = Mathf.CeilToInt(radius);
            
            for (int x = -intRadius; x <= intRadius; x++)
            {
                for (int z = -intRadius; z <= intRadius; z++)
                {
                    float distance = Mathf.Sqrt(x * x + z * z);
                    if (distance <= radius)
                    {
                        positions.Add(centerPosition + new Vector3Int(x, 0, z));
                    }
                }
            }
            
            return positions;
        }
    }
    
    /// <summary>
    /// Line brush
    /// </summary>
    public class LineBrush : TilemapBrush
    {
        private Vector3Int startPosition;
        private bool isDrawing = false;
        
        public LineBrush() : base(BrushType.Line) { }
        
        /// <summary>
        /// Start drawing line
        /// </summary>
        public void StartLine(Vector3Int position)
        {
            startPosition = position;
            isDrawing = true;
        }
        
        /// <summary>
        /// End drawing line
        /// </summary>
        public void EndLine()
        {
            isDrawing = false;
        }
        
        /// <summary>
        /// Get line positions from start to end
        /// </summary>
        public List<Vector3Int> GetLinePositions(Vector3Int endPosition)
        {
            if (!isDrawing) return new List<Vector3Int>();
            
            return GetLinePositions(startPosition, endPosition);
        }
        
        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;
            
            if (!isDrawing)
            {
                StartLine(position);
                return;
            }
            
            var positions = GetLinePositions(position);
            var changes = new List<TileChangeData>();
            
            foreach (var pos in positions)
            {
                var existingTile = tilemap.GetTile(pos, layerIndex);
                changes.Add(new TileChangeData
                {
                    position = pos,
                    layerIndex = layerIndex,
                    oldTileAsset = existingTile?.TileAsset,
                    newTileAsset = selectedTile,
                    changeType = TileChangeType.Place
                });
                
                tilemap.PlaceTile(pos, selectedTile, layerIndex);
            }
            
            tilemap.History.RecordBatchChanges(changes);
            EndLine();
        }
        
        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!isDrawing)
            {
                StartLine(position);
                return;
            }
            
            var positions = GetLinePositions(position);
            var changes = new List<TileChangeData>();
            
            foreach (var pos in positions)
            {
                var existingTile = tilemap.GetTile(pos, layerIndex);
                if (existingTile != null)
                {
                    changes.Add(new TileChangeData
                    {
                        position = pos,
                        layerIndex = layerIndex,
                        oldTileAsset = existingTile.TileAsset,
                        newTileAsset = null,
                        changeType = TileChangeType.Remove
                    });
                    
                    tilemap.RemoveTile(pos, layerIndex);
                }
            }
            
            if (changes.Count > 0)
            {
                tilemap.History.RecordBatchChanges(changes);
            }
            
            EndLine();
        }
        
        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            if (!isDrawing) return new List<Vector3Int> { centerPosition };
            return GetLinePositions(centerPosition);
        }
        
        /// <summary>
        /// Get line positions using Bresenham's algorithm
        /// </summary>
        private List<Vector3Int> GetLinePositions(Vector3Int start, Vector3Int end)
        {
            var positions = new List<Vector3Int>();
            
            int x0 = start.x, z0 = start.z;
            int x1 = end.x, z1 = end.z;
            
            int dx = Mathf.Abs(x1 - x0);
            int dz = Mathf.Abs(z1 - z0);
            
            int sx = x0 < x1 ? 1 : -1;
            int sz = z0 < z1 ? 1 : -1;
            
            int err = dx - dz;
            
            while (true)
            {
                positions.Add(new Vector3Int(x0, start.y, z0));
                
                if (x0 == x1 && z0 == z1) break;
                
                int e2 = 2 * err;
                
                if (e2 > -dz)
                {
                    err -= dz;
                    x0 += sx;
                }
                
                if (e2 < dx)
                {
                    err += dx;
                    z0 += sz;
                }
            }
            
            return positions;
        }
    }

    /// <summary>
    /// Flood fill brush
    /// </summary>
    public class FloodFillBrush : TilemapBrush
    {
        public FloodFillBrush() : base(BrushType.FloodFill) { }

        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            if (!CanPaint(tilemap, position, layerIndex)) return;

            var existingTile = tilemap.GetTile(position, layerIndex);
            var targetTileAsset = existingTile?.TileAsset;

            // Don't flood fill if target is same as selected tile
            if (targetTileAsset == selectedTile) return;

            var positions = GetFloodFillPositions(tilemap, position, layerIndex, targetTileAsset);
            var changes = new List<TileChangeData>();

            foreach (var pos in positions)
            {
                var tile = tilemap.GetTile(pos, layerIndex);
                changes.Add(new TileChangeData
                {
                    position = pos,
                    layerIndex = layerIndex,
                    oldTileAsset = tile?.TileAsset,
                    newTileAsset = selectedTile,
                    changeType = TileChangeType.Place
                });

                tilemap.PlaceTile(pos, selectedTile, layerIndex);
            }

            if (changes.Count > 0)
            {
                tilemap.History.RecordBatchChanges(changes);
            }
        }

        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var existingTile = tilemap.GetTile(position, layerIndex);
            if (existingTile == null) return;

            var targetTileAsset = existingTile.TileAsset;
            var positions = GetFloodFillPositions(tilemap, position, layerIndex, targetTileAsset);
            var changes = new List<TileChangeData>();

            foreach (var pos in positions)
            {
                var tile = tilemap.GetTile(pos, layerIndex);
                if (tile != null)
                {
                    changes.Add(new TileChangeData
                    {
                        position = pos,
                        layerIndex = layerIndex,
                        oldTileAsset = tile.TileAsset,
                        newTileAsset = null,
                        changeType = TileChangeType.Remove
                    });

                    tilemap.RemoveTile(pos, layerIndex);
                }
            }

            if (changes.Count > 0)
            {
                tilemap.History.RecordBatchChanges(changes);
            }
        }

        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            return new List<Vector3Int> { centerPosition };
        }

        /// <summary>
        /// Get flood fill positions using BFS
        /// </summary>
        private List<Vector3Int> GetFloodFillPositions(Tilemap3D tilemap, Vector3Int startPosition, int layerIndex, TileAsset targetTileAsset)
        {
            var positions = new List<Vector3Int>();
            var visited = new HashSet<Vector3Int>();
            var queue = new Queue<Vector3Int>();

            queue.Enqueue(startPosition);
            visited.Add(startPosition);

            // Directions for 4-connected flood fill
            var directions = new Vector3Int[]
            {
                new Vector3Int(1, 0, 0),
                new Vector3Int(-1, 0, 0),
                new Vector3Int(0, 0, 1),
                new Vector3Int(0, 0, -1)
            };

            while (queue.Count > 0 && positions.Count < 1000) // Limit to prevent infinite loops
            {
                var current = queue.Dequeue();
                positions.Add(current);

                foreach (var direction in directions)
                {
                    var neighbor = current + direction;

                    if (visited.Contains(neighbor)) continue;

                    var neighborTile = tilemap.GetTile(neighbor, layerIndex);
                    var neighborTileAsset = neighborTile?.TileAsset;

                    // Check if neighbor matches target tile
                    if (neighborTileAsset == targetTileAsset)
                    {
                        visited.Add(neighbor);
                        queue.Enqueue(neighbor);
                    }
                }
            }

            return positions;
        }
    }

    /// <summary>
    /// Eyedropper tool for picking tiles
    /// </summary>
    public class EyedropperBrush : TilemapBrush
    {
        public System.Action<TileAsset> OnTilePicked;

        public EyedropperBrush() : base(BrushType.Eyedropper) { }

        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            PickTile(tilemap, position, layerIndex);
        }

        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            PickTile(tilemap, position, layerIndex);
        }

        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            return new List<Vector3Int> { centerPosition };
        }

        /// <summary>
        /// Pick tile at position
        /// </summary>
        private void PickTile(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var tile = tilemap.GetTile(position, layerIndex);
            if (tile != null && tile.TileAsset != null)
            {
                selectedTile = tile.TileAsset;
                OnTilePicked?.Invoke(tile.TileAsset);
            }
        }
    }

    /// <summary>
    /// Eraser tool
    /// </summary>
    public class EraserBrush : TilemapBrush
    {
        public EraserBrush() : base(BrushType.Eraser) { }

        public override void Paint(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            Erase(tilemap, position, layerIndex);
        }

        public override void Erase(Tilemap3D tilemap, Vector3Int position, int layerIndex)
        {
            var positions = GetPreviewPositions(position);
            var changes = new List<TileChangeData>();

            foreach (var pos in positions)
            {
                var existingTile = tilemap.GetTile(pos, layerIndex);
                if (existingTile != null)
                {
                    changes.Add(new TileChangeData
                    {
                        position = pos,
                        layerIndex = layerIndex,
                        oldTileAsset = existingTile.TileAsset,
                        newTileAsset = null,
                        changeType = TileChangeType.Remove
                    });

                    tilemap.RemoveTile(pos, layerIndex);
                }
            }

            if (changes.Count > 0)
            {
                tilemap.History.RecordBatchChanges(changes);
            }
        }

        public override List<Vector3Int> GetPreviewPositions(Vector3Int centerPosition)
        {
            var positions = new List<Vector3Int>();
            int halfSize = brushSize / 2;

            for (int x = -halfSize; x <= halfSize; x++)
            {
                for (int z = -halfSize; z <= halfSize; z++)
                {
                    positions.Add(centerPosition + new Vector3Int(x, 0, z));
                }
            }

            return positions;
        }
    }
}
