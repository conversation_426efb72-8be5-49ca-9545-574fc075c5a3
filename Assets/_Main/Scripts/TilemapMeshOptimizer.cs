using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Tích hợp MeshOptimizer với Tilemap3D để tự động tối ưu hóa tiles
/// </summary>
[RequireComponent(typeof(Tilemap3D))]
public class TilemapMeshOptimizer : MonoBehaviour
{
    [System.Serializable]
    public class TilemapOptimizationSettings
    {
        [Header("Tự Động Tối Ưu")]
        public bool autoOptimizeOnTileChange = true;
        public int tilesThreshold = 10; // Số tiles tối thiểu để bắt đầu tối ưu
        public float optimizationDelay = 2f; // Delay sau khi thay đổi tile
        
        [Header("Tối Ưu Theo Vùng")]
        public bool useRegionalOptimization = true;
        public Vector2Int regionSize = new Vector2Int(16, 16);
        
        [Header("Material Grouping")]
        public bool groupByTileType = true;
        public bool preserveTileColliders = true;
    }
    
    [SerializeField] private TilemapOptimizationSettings settings = new TilemapOptimizationSettings();
    
    private Tilemap3D tilemap3D;
    private Dictionary<Vector2Int, MeshOptimizer> regionOptimizers = new Dictionary<Vector2Int, MeshOptimizer>();
    private float lastTileChangeTime;
    private bool needsOptimization = false;
    
    // Events
    public System.Action OnOptimizationStarted;
    public System.Action OnOptimizationCompleted;
    
    private void Awake()
    {
        tilemap3D = GetComponent<Tilemap3D>();
    }
    
    private void Start()
    {
        if (settings.autoOptimizeOnTileChange)
        {
            // Đăng ký events từ Tilemap3D nếu có
            SubscribeToTilemapEvents();
        }
    }
    
    private void Update()
    {
        if (needsOptimization && Time.time - lastTileChangeTime >= settings.optimizationDelay)
        {
            OptimizeTilemap();
            needsOptimization = false;
        }
    }
    
    /// <summary>
    /// Đăng ký events từ Tilemap3D
    /// </summary>
    private void SubscribeToTilemapEvents()
    {
        // Có thể mở rộng để lắng nghe events từ Tilemap3D
        // Hiện tại sẽ check thủ công
    }
    
    /// <summary>
    /// Gọi khi có tile được thay đổi
    /// </summary>
    public void OnTileChanged()
    {
        if (!settings.autoOptimizeOnTileChange) return;
        
        lastTileChangeTime = Time.time;
        needsOptimization = true;
    }
    
    /// <summary>
    /// Tối ưu hóa toàn bộ tilemap
    /// </summary>
    [ContextMenu("Tối Ưu Tilemap (Optimize Tilemap)")]
    public void OptimizeTilemap()
    {
        OnOptimizationStarted?.Invoke();
        
        if (settings.useRegionalOptimization)
        {
            OptimizeByRegions();
        }
        else
        {
            OptimizeEntireTilemap();
        }
        
        OnOptimizationCompleted?.Invoke();
        Debug.Log("✅ Tối ưu hóa Tilemap hoàn tất!");
    }
    
    /// <summary>
    /// Tối ưu hóa theo vùng
    /// </summary>
    private void OptimizeByRegions()
    {
        // Dọn dẹp optimizers cũ
        CleanupRegionOptimizers();
        
        // Tìm tất cả tiles
        Transform[] allTiles = GetAllTileTransforms();
        if (allTiles.Length < settings.tilesThreshold) return;
        
        // Nhóm tiles theo region
        Dictionary<Vector2Int, List<Transform>> regions = GroupTilesByRegion(allTiles);
        
        // Tạo optimizer cho mỗi region
        foreach (var region in regions)
        {
            if (region.Value.Count >= 2) // Cần ít nhất 2 tiles để gộp
            {
                CreateRegionOptimizer(region.Key, region.Value);
            }
        }
        
        Debug.Log($"Tạo {regionOptimizers.Count} region optimizers cho {allTiles.Length} tiles.");
    }
    
    /// <summary>
    /// Tối ưu hóa toàn bộ tilemap
    /// </summary>
    private void OptimizeEntireTilemap()
    {
        // Tạo một optimizer cho toàn bộ tilemap
        GameObject optimizerObject = new GameObject("TilemapOptimizer_All");
        optimizerObject.transform.SetParent(transform);
        optimizerObject.transform.localPosition = Vector3.zero;
        
        MeshOptimizer optimizer = optimizerObject.AddComponent<MeshOptimizer>();
        
        // Cấu hình optimizer
        ConfigureOptimizer(optimizer);
        
        // Di chuyển tất cả tiles vào optimizer
        Transform[] allTiles = GetAllTileTransforms();
        foreach (Transform tile in allTiles)
        {
            tile.SetParent(optimizerObject.transform);
        }
        
        // Chạy tối ưu hóa
        optimizer.OptimizeMeshes();
    }
    
    /// <summary>
    /// Lấy tất cả tile transforms
    /// </summary>
    private Transform[] GetAllTileTransforms()
    {
        List<Transform> tiles = new List<Transform>();
        
        for (int i = 0; i < transform.childCount; i++)
        {
            Transform child = transform.GetChild(i);
            if (child.GetComponent<MeshRenderer>() != null)
            {
                tiles.Add(child);
            }
        }
        
        return tiles.ToArray();
    }
    
    /// <summary>
    /// Nhóm tiles theo region
    /// </summary>
    private Dictionary<Vector2Int, List<Transform>> GroupTilesByRegion(Transform[] tiles)
    {
        Dictionary<Vector2Int, List<Transform>> regions = new Dictionary<Vector2Int, List<Transform>>();
        
        foreach (Transform tile in tiles)
        {
            Vector3 worldPos = tile.position;
            Vector2Int regionCoord = new Vector2Int(
                Mathf.FloorToInt(worldPos.x / settings.regionSize.x),
                Mathf.FloorToInt(worldPos.z / settings.regionSize.y)
            );
            
            if (!regions.ContainsKey(regionCoord))
            {
                regions[regionCoord] = new List<Transform>();
            }
            
            regions[regionCoord].Add(tile);
        }
        
        return regions;
    }
    
    /// <summary>
    /// Tạo optimizer cho một region
    /// </summary>
    private void CreateRegionOptimizer(Vector2Int regionCoord, List<Transform> tiles)
    {
        GameObject optimizerObject = new GameObject($"TilemapOptimizer_Region_{regionCoord.x}_{regionCoord.y}");
        optimizerObject.transform.SetParent(transform);
        
        // Đặt vị trí optimizer ở trung tâm region
        Vector3 centerPos = CalculateRegionCenter(tiles);
        optimizerObject.transform.position = centerPos;
        
        MeshOptimizer optimizer = optimizerObject.AddComponent<MeshOptimizer>();
        ConfigureOptimizer(optimizer);
        
        // Di chuyển tiles vào optimizer
        foreach (Transform tile in tiles)
        {
            tile.SetParent(optimizerObject.transform);
        }
        
        // Lưu optimizer
        regionOptimizers[regionCoord] = optimizer;
        
        // Chạy tối ưu hóa
        optimizer.OptimizeMeshes();
    }
    
    /// <summary>
    /// Tính toán trung tâm của region
    /// </summary>
    private Vector3 CalculateRegionCenter(List<Transform> tiles)
    {
        Vector3 center = Vector3.zero;
        foreach (Transform tile in tiles)
        {
            center += tile.position;
        }
        return center / tiles.Count;
    }
    
    /// <summary>
    /// Cấu hình optimizer theo settings
    /// </summary>
    private void ConfigureOptimizer(MeshOptimizer optimizer)
    {
        var optimizerSettings = optimizer.Settings;
        optimizerSettings.combineOnStart = false; // Sẽ gọi thủ công
        optimizerSettings.combineByMaterial = settings.groupByTileType;
        optimizerSettings.generateLODs = true;
        optimizerSettings.useSpatialPartitioning = false; // Đã chia region rồi
        optimizerSettings.preserveOriginalMeshes = false;
    }
    
    /// <summary>
    /// Dọn dẹp region optimizers
    /// </summary>
    private void CleanupRegionOptimizers()
    {
        foreach (var optimizer in regionOptimizers.Values)
        {
            if (optimizer != null)
            {
                optimizer.RestoreToOriginal();
                DestroyImmediate(optimizer.gameObject);
            }
        }
        regionOptimizers.Clear();
    }
    
    /// <summary>
    /// Khôi phục tilemap về trạng thái ban đầu
    /// </summary>
    [ContextMenu("Khôi Phục Tilemap (Restore Tilemap)")]
    public void RestoreTilemap()
    {
        CleanupRegionOptimizers();
        
        // Tìm và xóa tất cả optimizer objects
        MeshOptimizer[] optimizers = GetComponentsInChildren<MeshOptimizer>();
        foreach (MeshOptimizer optimizer in optimizers)
        {
            optimizer.RestoreToOriginal();
            DestroyImmediate(optimizer.gameObject);
        }
        
        Debug.Log("✅ Đã khôi phục Tilemap về trạng thái ban đầu!");
    }
    
    /// <summary>
    /// Thống kê tối ưu hóa tilemap
    /// </summary>
    [ContextMenu("Thống Kê Tilemap (Tilemap Stats)")]
    public void PrintTilemapStats()
    {
        Transform[] allTiles = GetAllTileTransforms();
        int totalOptimizers = regionOptimizers.Count;
        
        Debug.Log("=== THỐNG KÊ TILEMAP OPTIMIZER ===");
        Debug.Log($"Tổng số tiles: {allTiles.Length}");
        Debug.Log($"Số region optimizers: {totalOptimizers}");
        Debug.Log($"Kích thước region: {settings.regionSize}");
        Debug.Log($"Threshold: {settings.tilesThreshold} tiles");
        
        if (totalOptimizers > 0)
        {
            int totalCombinedObjects = 0;
            foreach (var optimizer in regionOptimizers.Values)
            {
                if (optimizer != null)
                {
                    totalCombinedObjects += optimizer.CombinedObjectsCount;
                }
            }
            Debug.Log($"Tổng combined objects: {totalCombinedObjects}");
        }
    }
    
    private void OnDestroy()
    {
        CleanupRegionOptimizers();
    }
}
